package com.ruoyi.vehicle.mapper;

import java.util.List;
import com.ruoyi.vehicle.domain.Car;
import org.apache.ibatis.annotations.Param;

/**
 * 我的车Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
public interface CarMapper 
{
    /**
     * 查询我的车
     * 
     * @param carId 我的车主键
     * @return 我的车
     */
    public Car selectCarByCarId(Long carId);

    /**
     * 查询我的车列表
     * 
     * @param car 我的车
     * @return 我的车集合
     */
    public List<Car> selectCarList(Car car);

    /**
     * 新增我的车
     * 
     * @param car 我的车
     * @return 结果
     */
    public int insertCar(Car car);

    /**
     * 修改我的车
     * 
     * @param car 我的车
     * @return 结果
     */
    public int updateCar(Car car);

    /**
     * 删除我的车
     * 
     * @param carId 我的车主键
     * @return 结果
     */
    public int deleteCarByCarId(Long carId);

    /**
     * 批量删除我的车
     * 
     * @param carIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCarByCarIds(Long[] carIds);

    /**
     * 将用户的所有车辆设置为维修中状态
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int updateAllUserCarsToMaintenance(Long userId);

    /**
     * 更新车辆状态
     * 
     * @param carId 车辆ID
     * @param status 状态
     * @return 结果
     */
    public int updateCarStatus(@Param("carId") Long carId, @Param("status") String status);

    /**
     * 查询用户当前在用车辆
     * 
     * @param userId 用户ID
     * @return 在用车辆信息
     */
    public Car selectInUseCarByUserId(Long userId);
}

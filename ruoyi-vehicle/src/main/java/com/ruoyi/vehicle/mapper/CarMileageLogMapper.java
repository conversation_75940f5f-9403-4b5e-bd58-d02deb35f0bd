package com.ruoyi.vehicle.mapper;

import java.util.List;
import com.ruoyi.vehicle.domain.CarMileageLog;

/**
 * 车辆里程校准日志Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
public interface CarMileageLogMapper {
    /**
     * 查询车辆里程校准日志列表
     *
     * @param carMileageLog 车辆里程校准日志
     * @return 车辆里程校准日志集合
     */
    public List<CarMileageLog> selectCarMileageLogList(CarMileageLog carMileageLog);

    /**
     * 新增车辆里程校准日志
     *
     * @param carMileageLog 车辆里程校准日志
     * @return 结果
     */
    public int insertCarMileageLog(CarMileageLog carMileageLog);
} 
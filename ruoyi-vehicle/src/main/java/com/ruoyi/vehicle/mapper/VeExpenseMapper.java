package com.ruoyi.vehicle.mapper;

import java.util.Date;
import java.util.List;
import com.ruoyi.vehicle.domain.VeExpense;

/**
 * 消费明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-20
 */
public interface VeExpenseMapper 
{
    /**
     * 查询消费明细
     * 
     * @param expenseId 消费明细主键
     * @return 消费明细
     */
    public VeExpense selectVeExpenseByExpenseId(Long expenseId);

    /**
     * 查询消费明细列表
     * 
     * @param veExpense 消费明细
     * @return 消费明细集合
     */
    public List<VeExpense> selectVeExpenseList(VeExpense veExpense);

    /**
     * 新增消费明细
     * 
     * @param veExpense 消费明细
     * @return 结果
     */
    public int insertVeExpense(VeExpense veExpense);

    /**
     * 修改消费明细
     * 
     * @param veExpense 消费明细
     * @return 结果
     */
    public int updateVeExpense(VeExpense veExpense);

    /**
     * 删除消费明细
     * 
     * @param expenseId 消费明细主键
     * @return 结果
     */
    public int deleteVeExpenseByExpenseId(Long expenseId);

    /**
     * 批量删除消费明细
     * 
     * @param expenseIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVeExpenseByExpenseIds(Long[] expenseIds);

    /**
     * 获取车辆最近的加油记录
     *
     * @param carId 车辆ID
     * @return 最近的加油记录
     */
    public VeExpense selectLastFuelExpense(Long carId);

    /**
     * 获取指定日期之前的最后一次加油记录
     *
     * @param carId 车辆ID
     * @param date 指定日期
     * @return 加油记录
     */
    public VeExpense selectLastFuelExpenseBeforeDate(Long carId, Date date);

    /**
     * 根据ID查询消费记录
     *
     * @param expenseId 消费记录ID
     * @return 消费记录
     */
    public VeExpense selectVeExpenseById(Long expenseId);
}

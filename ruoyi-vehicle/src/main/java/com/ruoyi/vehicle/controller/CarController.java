package com.ruoyi.vehicle.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.vehicle.domain.Car;
import com.ruoyi.vehicle.service.ICarService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;

/**
 * 我的车Controller
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
@RestController
@RequestMapping("/vehicle/car")
public class CarController extends BaseController
{
    @Autowired
    private ICarService carService;

    /**
     * 查询我的车列表
     */
    @PreAuthorize("@ss.hasPermi('vehicle:car:list')")
    @GetMapping("/list")
    @DataScope(userAlias = "c")
    public TableDataInfo list(Car car)
    {
        startPage();
        List<Car> list = carService.selectCarList(car);
        return getDataTable(list);
    }

    /**
     * 导出我的车列表
     */
    @PreAuthorize("@ss.hasPermi('vehicle:car:export')")
    @Log(title = "我的车", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Car car)
    {
        List<Car> list = carService.selectCarList(car);
        ExcelUtil<Car> util = new ExcelUtil<Car>(Car.class);
        util.exportExcel(response, list, "我的车数据");
    }

    /**
     * 获取我的车详细信息
     */
    @PreAuthorize("@ss.hasPermi('vehicle:car:query')")
    @GetMapping(value = "/{carId}")
    public AjaxResult getInfo(@PathVariable("carId") Long carId)
    {
        return success(carService.selectCarByCarId(carId));
    }

    /**
     * 新增我的车
     */
    @PreAuthorize("@ss.hasPermi('vehicle:car:add')")
    @Log(title = "我的车", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Car car)
    {
        car.setCreateBy(getUsername());
        // 插入车辆
        int insertResult = carService.insertCar(car);
        if (insertResult > 0) {
            // 插入成功后调用设置在用车辆的方法
            carService.setCarInUse(car.getCarId());
        }
        return toAjax(insertResult);
    }

    /**
     * 修改我的车
     */
    @PreAuthorize("@ss.hasPermi('vehicle:car:edit')")
    @Log(title = "我的车", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Car car)
    {
        // 如果修改了里程，更新校准时间
        Car oldCar = carService.selectCarByCarId(car.getCarId());
        if (oldCar != null && car.getTotalMileage() != null && !car.getTotalMileage().equals(oldCar.getTotalMileage())) {
            car.setLastMileageCalibration(DateUtils.getNowDate());
        }
        
        carService.setCarInUse(car.getCarId());
        car.setUpdateBy(getUsername());
        return toAjax(carService.updateCar(car));
    }

    /**
     * 删除我的车
     */
    @PreAuthorize("@ss.hasPermi('vehicle:car:remove')")
    @Log(title = "我的车", businessType = BusinessType.DELETE)
	@DeleteMapping("/{carIds}")
    public AjaxResult remove(@PathVariable Long[] carIds)
    {
        return toAjax(carService.deleteCarByCarIds(carIds));
    }

    /**
     * 设置在用车辆
     */
    @PreAuthorize("@ss.hasPermi('vehicle:car:edit')")
    @Log(title = "设置在用车辆", businessType = BusinessType.UPDATE)
    @PutMapping("/setInUse/{carId}")
    public AjaxResult setInUse(@PathVariable("carId") Long carId) {
        return toAjax(carService.setCarInUse(carId));
    }

    /**
     * 获取当前用户在用车辆
     */
    @PreAuthorize("@ss.hasPermi('vehicle:car:query')")
    @GetMapping("/getCurrentInUse")
    public AjaxResult getCurrentInUse() {
        return success(carService.selectCurrentInUseCar());
    }
}

package com.ruoyi.vehicle.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.vehicle.domain.VeExpense;
import com.ruoyi.vehicle.service.IVeExpenseService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.annotation.DataScope;

/**
 * 消费明细Controller
 * 
 * <AUTHOR>
 * @date 2024-11-20
 */
@RestController
@RequestMapping("/vehicle/expense")
public class VeExpenseController extends BaseController
{
    @Autowired
    private IVeExpenseService veExpenseService;

    /**
     * 查询消费明细列表
     */
    @PreAuthorize("@ss.hasPermi('vehicle:expense:list')")
    @GetMapping("/list")
    @DataScope(userAlias = "u")
    public TableDataInfo list(VeExpense veExpense)
    {
        startPage();
        List<VeExpense> list = veExpenseService.selectVeExpenseList(veExpense);
        return getDataTable(list);
    }

    /**
     * 导出消费明细列表
     */
    @PreAuthorize("@ss.hasPermi('vehicle:expense:export')")
    @Log(title = "消费明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VeExpense veExpense)
    {
        List<VeExpense> list = veExpenseService.selectVeExpenseList(veExpense);
        ExcelUtil<VeExpense> util = new ExcelUtil<VeExpense>(VeExpense.class);
        util.exportExcel(response, list, "消费明细数据");
    }

    /**
     * 获取消费明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('vehicle:expense:query')")
    @GetMapping(value = "/{expenseId}")
    public AjaxResult getInfo(@PathVariable("expenseId") Long expenseId)
    {
        return success(veExpenseService.selectVeExpenseByExpenseId(expenseId));
    }

    /**
     * 新增消费明细
     */
    @PreAuthorize("@ss.hasPermi('vehicle:expense:add')")
    @Log(title = "消费明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VeExpense veExpense)
    {
        veExpense.setCreateBy(getUsername());
        return toAjax(veExpenseService.insertVeExpense(veExpense));
    }

    /**
     * 修改消费明细
     */
    @PreAuthorize("@ss.hasPermi('vehicle:expense:edit')")
    @Log(title = "消费明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VeExpense veExpense)
    {
        veExpense.setUpdateBy(getUsername());
        return toAjax(veExpenseService.updateVeExpense(veExpense));
    }

    /**
     * 删除消费明细
     */
    @PreAuthorize("@ss.hasPermi('vehicle:expense:remove')")
    @Log(title = "消费明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{expenseIds}")
    public AjaxResult remove(@PathVariable Long[] expenseIds)
    {
        return toAjax(veExpenseService.deleteVeExpenseByExpenseIds(expenseIds));
    }
}

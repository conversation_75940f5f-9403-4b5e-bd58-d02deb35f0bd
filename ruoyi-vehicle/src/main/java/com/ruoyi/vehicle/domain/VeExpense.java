package com.ruoyi.vehicle.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 消费明细对象 ve_expense
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
public class VeExpense extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 费用ID
     */
    private Long expenseId;

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 消费类型
     */
    @Excel(name = "消费类型")
    private Long type;

    /**
     * 消费日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "消费日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date date;

    /**
     * 消费金额
     */
    @Excel(name = "消费金额")
    private BigDecimal spent;

    /**
     * 费用描述
     */
    @Excel(name = "费用描述")
    private String description;

    /**
     * 每升价格
     */
    @Excel(name = "每升价格")
    private BigDecimal pricePerLiter;

    /**
     * 总里程(米)
     */
    @Excel(name = "总里程(米)")
    private Long totalMeters;

    /**
     * 车辆
     */
    private Long carId;

    /**
     * 用户
     */
    @Excel(name = "用户")
    private Long userId;

    /**
     * 是否删除
     */
    @Excel(name = "是否删除")
    private Boolean isDeleted;

    /** 同步状态 */
    @Excel(name = "同步状态")
    private Integer syncStatus; // 新增字段，例如使用整型表示状态

    /** 渠道信息 */
    @Excel(name = "渠道信息")
    private String channel;

    /** 用户名称 */
    @Excel(name = "用户名称")
    private String userName;

    public void setIsDeleted(Boolean isDeleted) { // 新增setter方法
        this.isDeleted = isDeleted;
    }

    public Boolean getIsDeleted() { // 新增getter方法
        return isDeleted;
    }

    public void setSyncStatus(Integer syncStatus) { // 新增setter方法
        this.syncStatus = syncStatus;
    }

    public Integer getSyncStatus() { // 新增getter方法
        return syncStatus;
    }

    public void setChannel(String channel) { // 新增setter方法
        this.channel = channel;
    }

    public String getChannel() { // 新增getter方法
        return channel;
    }

    public void setExpenseId(Long expenseId) {
        this.expenseId = expenseId;
    }

    public Long getExpenseId() {
        return expenseId;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public Long getType() {
        return type;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Date getDate() {
        return date;
    }

    public void setSpent(BigDecimal spent) {
        this.spent = spent;
    }

    public BigDecimal getSpent() {
        return spent;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setPricePerLiter(BigDecimal pricePerLiter) {
        this.pricePerLiter = pricePerLiter;
    }

    public BigDecimal getPricePerLiter() {
        return pricePerLiter;
    }

    public void setTotalMeters(Long totalMeters) {
        this.totalMeters = totalMeters;
    }

    public Long getTotalMeters() {
        return totalMeters;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }

    public Long getCarId() {
        return carId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("expenseId", getExpenseId())
                .append("title", getTitle())
                .append("type", getType())
                .append("date", getDate())
                .append("spent", getSpent())
                .append("description", getDescription())
                .append("pricePerLiter", getPricePerLiter())
                .append("totalMeters", getTotalMeters())
                .append("carId", getCarId())
                .append("userId", getUserId())
                .append("isDeleted", getIsDeleted())
                .append("syncStatus", getSyncStatus())
                .append("channel", getChannel())
                .append("userName", getUserName())
                .toString();
    }
}

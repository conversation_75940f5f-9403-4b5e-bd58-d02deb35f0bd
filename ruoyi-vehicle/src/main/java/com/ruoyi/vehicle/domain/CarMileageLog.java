package com.ruoyi.vehicle.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.annotation.Excel;

/**
 * 车辆里程校准日志对象 car_mileage_log
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
public class CarMileageLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long logId;

    /** 车辆ID */
    @Excel(name = "车辆ID")
    private Long carId;

    /** 原里程 */
    @Excel(name = "原里程")
    private Long originalMileage;

    /** 新里程 */
    @Excel(name = "新里程")
    private Long newMileage;

    /** 调整量 */
    @Excel(name = "调整量")
    private Long adjustment;

    /** 校准原因 */
    @Excel(name = "校准原因")
    private String reason;

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Long getLogId() {
        return logId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }

    public Long getCarId() {
        return carId;
    }

    public void setOriginalMileage(Long originalMileage) {
        this.originalMileage = originalMileage;
    }

    public Long getOriginalMileage() {
        return originalMileage;
    }

    public void setNewMileage(Long newMileage) {
        this.newMileage = newMileage;
    }

    public Long getNewMileage() {
        return newMileage;
    }

    public void setAdjustment(Long adjustment) {
        this.adjustment = adjustment;
    }

    public Long getAdjustment() {
        return adjustment;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getReason() {
        return reason;
    }
} 
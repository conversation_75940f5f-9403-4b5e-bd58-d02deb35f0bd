package com.ruoyi.vehicle.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excels;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 我的车对象 car
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
public class Car extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 汽车ID */
    private Long carId;

    /** 品牌ID */
    private String brandId;

    /** 品牌名称 */
    @Excel(name = "品牌")
    private String brand;

    /** 型号ID */
    private String modelId;

    /** 型号名称 */
    @Excel(name = "型号")
    private String model;

    /** 燃料类型 */
    private String fuelType;

    /** 排放等级 */
    private String euroCategory;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String licensePlate;

    /** 行驶总里程 */
    @Excel(name = "行驶总里程")
    private Long totalMileage;

    /** 小计里程 */
    private Long subtotalMileage;

    /** 是否装有ETC设备 */
    private String hasEtc;

    /** 图片 */
    private String carModelImage;

    /** 用户ID */
    private Long userId;

    /** 用户名称 */
    @Excel(name = "用户")
    private String userName;

    /** 汽车颜色 */
    private String color;

    /** 车辆状态 */
    @Excel(name = "车辆状态")
    private String status;

    /** 品牌Logo */
    private String brandLogo;

    /** 是否删除 */
    private Integer delFlag;

    /** 总驾驶时间(秒) */
    @Excel(name = "总驾驶时间")
    private Long totalDrivingTime;

    /** 格式化后的总驾驶时间 */
    private String formattedDrivingTime;
    
    /** 最后一次里程校准日期 */
    private Date lastMileageCalibration;

    public void setCarId(Long carId) 
    {
        this.carId = carId;
    }

    public Long getCarId() 
    {
        return carId;
    }
    public void setBrand(String brand) 
    {
        this.brand = brand;
    }

    public String getBrand() 
    {
        return brand;
    }
    public void setModel(String model) 
    {
        this.model = model;
    }

    public String getModel() 
    {
        return model;
    }
    public void setFuelType(String fuelType) 
    {
        this.fuelType = fuelType;
    }

    public String getFuelType() 
    {
        return fuelType;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
    public String getUserName() {
        return userName;
    }

    public void setEuroCategory(String euroCategory)
    {
        this.euroCategory = euroCategory;
    }

    public String getEuroCategory() 
    {
        return euroCategory;
    }
    public void setLicensePlate(String licensePlate) 
    {
        this.licensePlate = licensePlate;
    }

    public String getLicensePlate() 
    {
        return licensePlate;
    }
    public void setTotalMileage(Long totalMileage) 
    {
        this.totalMileage = totalMileage;
    }

    public Long getTotalMileage() 
    {
        return totalMileage;
    }
    public void setSubtotalMileage(Long subtotalMileage) 
    {
        this.subtotalMileage = subtotalMileage;
    }

    public Long getSubtotalMileage() 
    {
        return subtotalMileage;
    }
    public void setHasEtc(String hasEtc)
    {
        this.hasEtc = hasEtc;
    }

    public String getHasEtc()
    {
        return hasEtc;
    }
    public void setCarModelImage(String carModelImage) 
    {
        this.carModelImage = carModelImage;
    }

    public String getCarModelImage() 
    {
        return carModelImage;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setColor(String color) 
    {
        this.color = color;
    }

    public String getColor() 
    {
        return color;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getBrandLogo() {
        return brandLogo;
    }

    public void setBrandLogo(String brandLogo) {
        this.brandLogo = brandLogo;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setTotalDrivingTime(Long totalDrivingTime) {
        this.totalDrivingTime = totalDrivingTime;
        this.formattedDrivingTime = formatDrivingTime(totalDrivingTime);
    }

    public Long getTotalDrivingTime() {
        return totalDrivingTime;
    }

    public String getFormattedDrivingTime() {
        if (formattedDrivingTime == null && totalDrivingTime != null) {
            formattedDrivingTime = formatDrivingTime(totalDrivingTime);
        }
        return formattedDrivingTime;
    }

    /**
     * 将毫秒数转换为格式化的时间字符串
     * 
     * @param milliseconds 总毫秒数
     * @return 格式化后的时间字符串（例如：2天5小时30分钟）
     */
    private String formatDrivingTime(Long milliseconds) {
        if (milliseconds == null || milliseconds == 0) {
            return "0分钟";
        }

        // 转换为秒
        long seconds = milliseconds / 1000;
        long days = seconds / (24 * 3600);
        long hours = (seconds % (24 * 3600)) / 3600;
        long minutes = (seconds % 3600) / 60;

        StringBuilder result = new StringBuilder();
        if (days > 0) {
            result.append(days).append("天");
        }
        if (hours > 0) {
            result.append(hours).append("小时");
        }
        if (minutes > 0 || (days == 0 && hours == 0)) {
            result.append(minutes).append("分钟");
        }

        return result.toString();
    }

    public Date getLastMileageCalibration() {
        return lastMileageCalibration;
    }

    public void setLastMileageCalibration(Date lastMileageCalibration) {
        this.lastMileageCalibration = lastMileageCalibration;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("carId", getCarId())
            .append("brand", getBrand())
            .append("model", getModel())
            .append("fuelType", getFuelType())
            .append("euroCategory", getEuroCategory())
            .append("licensePlate", getLicensePlate())
            .append("totalMileage", getTotalMileage())
            .append("subtotalMileage", getSubtotalMileage())
            .append("hasEtc", getHasEtc())
            .append("carModelImage", getCarModelImage())
            .append("userId", getUserId())
            .append("color", getColor())
            .append("status", getStatus())
            .append("totalDrivingTime", getTotalDrivingTime())
            .append("formattedDrivingTime", getFormattedDrivingTime())
            .toString();
    }
}

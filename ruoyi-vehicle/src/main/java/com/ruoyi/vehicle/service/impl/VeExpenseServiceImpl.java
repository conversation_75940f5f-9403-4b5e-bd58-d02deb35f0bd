package com.ruoyi.vehicle.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.vehicle.mapper.VeExpenseMapper;
import com.ruoyi.vehicle.domain.VeExpense;
import com.ruoyi.vehicle.service.IVeExpenseService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.vehicle.domain.Car;
import com.ruoyi.vehicle.service.ICarService;

/**
 * 消费明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-20
 */
@Service
public class VeExpenseServiceImpl implements IVeExpenseService 
{
    @Autowired
    private VeExpenseMapper veExpenseMapper;

    @Autowired
    private ICarService carService;

    /**
     * 查询消费明细
     * 
     * @param expenseId 消费明细主键
     * @return 消费明细
     */
    @Override
    public VeExpense selectVeExpenseByExpenseId(Long expenseId)
    {
        return veExpenseMapper.selectVeExpenseByExpenseId(expenseId);
    }

    /**
     * 查询消费明细列表
     * 
     * @param veExpense 消费明细
     * @return 消费明细
     */
    @Override
    public List<VeExpense> selectVeExpenseList(VeExpense veExpense)
    {
        // 如果不是管理员,只能查看自己的消费记录
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId()))
        {
            veExpense.setUserId(SecurityUtils.getUserId());
        }
        return veExpenseMapper.selectVeExpenseList(veExpense);
    }

    /**
     * 新增消费明细
     * 
     * @param veExpense 消费明细
     * @return 结果
     */
    @Override
    @Transactional
    public int insertVeExpense(VeExpense veExpense)
    {
        // 如果是加油记录，验证并更新里程
        if (veExpense.getType() != null && veExpense.getType() == 0) { // 加油类型为0
            validateAndUpdateMileage(veExpense, null);
        }
        
        veExpense.setCreateTime(DateUtils.getNowDate());
        return veExpenseMapper.insertVeExpense(veExpense);
    }

    /**
     * 修改消费明细
     * 
     * @param veExpense 消费明细
     * @return 结果
     */
    @Override
    @Transactional
    public int updateVeExpense(VeExpense veExpense)
    {
        // 如果是加油记录，验证并更新里程
        if (veExpense.getType() != null && veExpense.getType() == 0) {
            VeExpense oldExpense = veExpenseMapper.selectVeExpenseByExpenseId(veExpense.getExpenseId());
            validateAndUpdateMileage(veExpense, oldExpense);
        }
        
        veExpense.setUpdateTime(DateUtils.getNowDate());
        return veExpenseMapper.updateVeExpense(veExpense);
    }

    /**
     * 验证并更新里程信息
     */
    private void validateAndUpdateMileage(VeExpense newExpense, VeExpense oldExpense) {
        if (newExpense.getCarId() == null || newExpense.getTotalMeters() == null) {
            throw new ServiceException("加油记录必须包含车辆和里程信息");
        }

        Car car = carService.selectCarByCarId(newExpense.getCarId());
        if (car == null) {
            throw new ServiceException("车辆不存在");
        }

        System.out.println("开始验证里程 - 车辆ID: " + car.getCarId() + ", 当前里程: " + car.getTotalMileage() + ", 加油记录里程: " + newExpense.getTotalMeters());

        // 获取该车辆最新的加油记录
        VeExpense lastExpense = veExpenseMapper.selectLastFuelExpense(newExpense.getCarId());
        System.out.println("最新的加油记录: " + (lastExpense != null ? "ID=" + lastExpense.getExpenseId() + ", 日期=" + lastExpense.getDate() + ", 里程=" + lastExpense.getTotalMeters() : "无"));

        // 如果是更新操作，且更新的就是最新的加油记录，则使用上上次的记录进行验证
        if (oldExpense != null && lastExpense != null && lastExpense.getExpenseId().equals(oldExpense.getExpenseId())) {
            System.out.println("正在更新最新的加油记录，获取上上次记录进行验证");
            lastExpense = veExpenseMapper.selectLastFuelExpenseBeforeDate(newExpense.getCarId(), lastExpense.getDate());
            System.out.println("上上次加油记录: " + (lastExpense != null ? "ID=" + lastExpense.getExpenseId() + ", 日期=" + lastExpense.getDate() + ", 里程=" + lastExpense.getTotalMeters() : "无"));
        }

        // 不再验证里程合理性，由用户自行决定

        try {
            // 判断是否需要更新车辆总里程
            boolean shouldUpdateMileage = false;
            
            // 获取该车辆最新的加油记录（不考虑当前记录）
            VeExpense latestExpense = veExpenseMapper.selectLastFuelExpense(newExpense.getCarId());
            
            if (latestExpense == null) {
                // 如果没有任何加油记录，这是第一条记录，应该更新
                shouldUpdateMileage = true;
            } else if (oldExpense != null && oldExpense.getExpenseId().equals(latestExpense.getExpenseId())) {
                // 如果正在更新最新记录，获取上一条记录进行比较
                latestExpense = veExpenseMapper.selectLastFuelExpenseBeforeDate(newExpense.getCarId(), latestExpense.getDate());
                shouldUpdateMileage = latestExpense == null || newExpense.getDate().after(latestExpense.getDate());
            } else {
                // 如果是新记录，判断日期是否是最新的
                shouldUpdateMileage = newExpense.getDate().after(latestExpense.getDate());
            }

            if (shouldUpdateMileage) {
                System.out.println("新记录日期(" + newExpense.getDate() + ")晚于最新记录日期(" + 
                    (lastExpense != null ? lastExpense.getDate() : "无") + ")，更新车辆总里程为: " + newExpense.getTotalMeters());
                
                // 更新车辆总里程
                car.setLastMileageCalibration(DateUtils.getNowDate());
                car.setTotalMileage(newExpense.getTotalMeters());
                
                int result = carService.updateCar(car);
                System.out.println("车辆里程更新结果: " + result);
                
                if (result <= 0) {
                    throw new ServiceException("更新车辆里程失败");
                }
            } else {
                System.out.println("新记录日期(" + newExpense.getDate() + ")不是最新，不更新车辆总里程");
            }
        } catch (Exception e) {
            System.err.println("更新车辆里程失败: " + e.getMessage());
            e.printStackTrace();
            throw new ServiceException("更新车辆里程时发生错误: " + e.getMessage());
        }
    }

    /**
     * 批量删除消费明细
     * 
     * @param expenseIds 需要删除的消费明细主键
     * @return 结果
     */
    @Override
    public int deleteVeExpenseByExpenseIds(Long[] expenseIds)
    {
        return veExpenseMapper.deleteVeExpenseByExpenseIds(expenseIds);
    }

    /**
     * 删除消费明细信息
     * 
     * @param expenseId 消费明细主键
     * @return 结果
     */
    @Override
    public int deleteVeExpenseByExpenseId(Long expenseId)
    {
        return veExpenseMapper.deleteVeExpenseByExpenseId(expenseId);
    }
}

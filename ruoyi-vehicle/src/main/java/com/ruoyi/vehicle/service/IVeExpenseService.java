package com.ruoyi.vehicle.service;

import java.util.List;
import com.ruoyi.vehicle.domain.VeExpense;

/**
 * 消费明细Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-20
 */
public interface IVeExpenseService 
{
    /**
     * 查询消费明细
     * 
     * @param expenseId 消费明细主键
     * @return 消费明细
     */
    public VeExpense selectVeExpenseByExpenseId(Long expenseId);

    /**
     * 查询消费明细列表
     * 
     * @param veExpense 消费明细
     * @return 消费明细集合
     */
    public List<VeExpense> selectVeExpenseList(VeExpense veExpense);

    /**
     * 新增消费明细
     * 
     * @param veExpense 消费明细
     * @return 结果
     */
    public int insertVeExpense(VeExpense veExpense);

    /**
     * 修改消费明细
     * 
     * @param veExpense 消费明细
     * @return 结果
     */
    public int updateVeExpense(VeExpense veExpense);

    /**
     * 批量删除消费明细
     * 
     * @param expenseIds 需要删除的消费明细主键集合
     * @return 结果
     */
    public int deleteVeExpenseByExpenseIds(Long[] expenseIds);

    /**
     * 删除消费明细信息
     * 
     * @param expenseId 消费明细主键
     * @return 结果
     */
    public int deleteVeExpenseByExpenseId(Long expenseId);
}

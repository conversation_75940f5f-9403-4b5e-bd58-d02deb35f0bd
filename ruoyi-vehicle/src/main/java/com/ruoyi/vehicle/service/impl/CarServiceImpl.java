package com.ruoyi.vehicle.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.vehicle.mapper.CarMapper;
import com.ruoyi.vehicle.domain.Car;
import com.ruoyi.vehicle.service.ICarService;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.vehicle.mapper.CarMileageLogMapper;
import com.ruoyi.vehicle.domain.CarMileageLog;

/**
 * 我的车Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
@Service
public class CarServiceImpl implements ICarService 
{
    @Autowired
    private CarMapper carMapper;

    @Autowired
    private CarMileageLogMapper carMileageLogMapper;

    /**
     * 查询我的车
     * 
     * @param carId 我的车主键
     * @return 我的车
     */
    @Override
    public Car selectCarByCarId(Long carId)
    {
        return carMapper.selectCarByCarId(carId);
    }

    /**
     * 查询我的车列表
     * 
     * @param car 我的车
     * @return 我的车
     */
    @Override
    public List<Car> selectCarList(Car car)
    {
        // 如果不是管理员,只能查看自己的车辆
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId()))
        {
            car.setUserId(SecurityUtils.getUserId());
        }
        return carMapper.selectCarList(car);
    }

    /**
     * 新增我的车
     * 
     * @param car 我的车
     * @return 结果
     */
    @Override
    public int insertCar(Car car)
    {
        // 设置创建人ID为当前用户
        car.setUserId(SecurityUtils.getUserId());
        return carMapper.insertCar(car);
    }

    /**
     * 修改我的车
     * 
     * @param car 我的车
     * @return 结果
     */
    @Override
    public int updateCar(Car car)
    {
        return carMapper.updateCar(car);
    }

    /**
     * 批量删除我的车
     * 
     * @param carIds 需要删除的我的车主键
     * @return 结果
     */
    @Override
    public int deleteCarByCarIds(Long[] carIds)
    {
        return carMapper.deleteCarByCarIds(carIds);
    }

    /**
     * 删除我的车信息
     * 
     * @param carId 我的车主键
     * @return 结果
     */
    @Override
    public int deleteCarByCarId(Long carId)
    {
        return carMapper.deleteCarByCarId(carId);
    }

    /**
     * 设置车辆为在用状态
     * 
     * @param carId 车辆ID
     * @return 结果
     */
    @Override
    @Transactional
    public int setCarInUse(Long carId) {
        Long userId = SecurityUtils.getUserId();

        // 先将该用户的所有车辆设置为维修中状态
        carMapper.updateAllUserCarsToMaintenance(userId);

        // 再将指定车辆设置为在用状态
        return carMapper.updateCarStatus(carId, "0"); // 假设0表示在用状态
    }

    /**
     * 获取当前用户在用车辆
     * 
     * @return 在用车辆信息
     */
    @Override
    public Car selectCurrentInUseCar() {
        Long userId = SecurityUtils.getUserId();
        return carMapper.selectInUseCarByUserId(userId);
    }

    @Override
    @Transactional
    public int calibrateMileage(Long carId, Long actualMileage, String reason) {
        // 获取车辆信息
        Car car = selectCarByCarId(carId);
        if (car == null) {
            throw new ServiceException("车辆不存在");
        }

        // 假设客户端提供的里程已经是米单位，不需要转换
        Long actualMileageInMeters = actualMileage;

        // 记录校准日志
        CarMileageLog log = new CarMileageLog();
        log.setCarId(carId);
        log.setOriginalMileage(car.getTotalMileage());
        log.setNewMileage(actualMileageInMeters);
        log.setAdjustment(actualMileageInMeters - car.getTotalMileage());
        log.setReason(reason);
        log.setCreateBy(SecurityUtils.getUsername());
        log.setCreateTime(DateUtils.getNowDate());
        carMileageLogMapper.insertCarMileageLog(log);

        // 更新车辆里程
        car.setTotalMileage(actualMileageInMeters);
        // 记录校准时间
        car.setLastMileageCalibration(DateUtils.getNowDate());
        return updateCar(car);
    }
}

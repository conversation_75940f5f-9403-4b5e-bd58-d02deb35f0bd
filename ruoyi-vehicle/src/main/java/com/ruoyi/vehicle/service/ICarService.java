package com.ruoyi.vehicle.service;

import java.util.List;
import com.ruoyi.vehicle.domain.Car;

/**
 * 我的车Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
public interface ICarService 
{
    /**
     * 查询我的车
     * 
     * @param carId 我的车主键
     * @return 我的车
     */
    public Car selectCarByCarId(Long carId);

    /**
     * 查询我的车列表
     * 
     * @param car 我的车
     * @return 我的车集合
     */
    public List<Car> selectCarList(Car car);

    /**
     * 新增我的车
     * 
     * @param car 我的车
     * @return 结果
     */
    public int insertCar(Car car);

    /**
     * 修改我的车
     * 
     * @param car 我的车
     * @return 结果
     */
    public int updateCar(Car car);

    /**
     * 批量删除我的车
     * 
     * @param carIds 需要删除的我的车主键集合
     * @return 结果
     */
    public int deleteCarByCarIds(Long[] carIds);

    /**
     * 删除我的车信息
     * 
     * @param carId 我的车主键
     * @return 结果
     */
    public int deleteCarByCarId(Long carId);

    /**
     * 设置车辆为在用状态
     * 
     * @param carId 车辆ID
     * @return 结果
     */
    public int setCarInUse(Long carId);

    /**
     * 获取当前用户在用车辆
     * 
     * @return 在用车辆信息
     */
    public Car selectCurrentInUseCar();

    /**
     * 校准车辆里程
     *
     * @param carId 车辆ID
     * @param actualMileage 实际里程
     * @param reason 校准原因
     * @return 结果
     */
    public int calibrateMileage(Long carId, Long actualMileage, String reason);
}

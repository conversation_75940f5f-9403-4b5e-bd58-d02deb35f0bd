<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vehicle.mapper.VeExpenseMapper">
    
    <resultMap type="VeExpense" id="VeExpenseResult">
        <result property="expenseId"    column="expense_id"    />
        <result property="title"    column="title"    />
        <result property="type"    column="type"    />
        <result property="date"    column="date"    />
        <result property="spent"    column="spent"    />
        <result property="description"    column="description"    />
        <result property="pricePerLiter"    column="price_per_liter"    />
        <result property="totalMeters"    column="total_meters"    />
        <result property="carId"    column="car_id"    />
        <result property="userId"    column="user_id"    />
        <result property="isDeleted" column="is_deleted" />
        <result property="syncStatus" column="sync_status" />
        <result property="channel" column="channel" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="userName" column="user_name" />
    </resultMap>

    <sql id="selectVeExpenseVo">
        select e.expense_id, 
               e.title, 
               e.type, 
               e.date, 
               e.spent, 
               e.description, 
               e.price_per_liter, 
               e.total_meters, 
               e.car_id, 
               e.user_id,
               e.is_deleted,
               e.sync_status,
               e.channel,
               e.create_by,
               e.create_time,
               e.update_by,
               e.update_time,
               u.nick_name as user_name
        from ve_expense e
        left join sys_user u on e.user_id = u.user_id
    </sql>

    <select id="selectVeExpenseList" parameterType="VeExpense" resultMap="VeExpenseResult">
        <include refid="selectVeExpenseVo"/>
        <where>
            <if test="type != null"> and e.type = #{type}</if>
            <if test="date != null">
                and YEAR(e.date) = YEAR(#{date}) and MONTH(e.date) = MONTH(#{date})
            </if>
            <if test="carId != null"> and e.car_id = #{carId}</if>
            <if test="userId != null"> and e.user_id = #{userId}</if>
            <if test="userName != null and userName != ''">
                AND u.nick_name like concat('%', #{userName}, '%')
            </if>
            ${params.dataScope}
        </where>
    </select>
    
    <select id="selectVeExpenseByExpenseId" parameterType="Long" resultMap="VeExpenseResult">
        <include refid="selectVeExpenseVo"/>
        where expense_id = #{expenseId}
    </select>

    <insert id="insertVeExpense" parameterType="VeExpense">
        insert into ve_expense
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="type != null">type,</if>
            <if test="date != null">date,</if>
            <if test="spent != null">spent,</if>
            <if test="description != null">description,</if>
            <if test="pricePerLiter != null">price_per_liter,</if>
            <if test="totalMeters != null">total_meters,</if>
            <if test="carId != null">car_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="syncStatus != null">sync_status,</if>
            <if test="channel != null">channel,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="type != null">#{type},</if>
            <if test="date != null">#{date},</if>
            <if test="spent != null">#{spent},</if>
            <if test="description != null">#{description},</if>
            <if test="pricePerLiter != null">#{pricePerLiter},</if>
            <if test="totalMeters != null">#{totalMeters},</if>
            <if test="carId != null">#{carId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="syncStatus != null">#{syncStatus},</if>
            <if test="channel != null">#{channel},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateVeExpense" parameterType="VeExpense">
        update ve_expense
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="type != null">type = #{type},</if>
            <if test="date != null">date = #{date},</if>
            <if test="spent != null">spent = #{spent},</if>
            <if test="description != null">description = #{description},</if>
            <if test="pricePerLiter != null">price_per_liter = #{pricePerLiter},</if>
            <if test="totalMeters != null">total_meters = #{totalMeters},</if>
            <if test="carId != null">car_id = #{carId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="syncStatus != null">sync_status = #{syncStatus},</if>
            <if test="channel != null">channel = #{channel},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where expense_id = #{expenseId}
    </update>

    <delete id="deleteVeExpenseByExpenseId" parameterType="Long">
        delete from ve_expense where expense_id = #{expenseId}
    </delete>

    <delete id="deleteVeExpenseByExpenseIds" parameterType="String">
        delete from ve_expense where expense_id in 
        <foreach item="expenseId" collection="array" open="(" separator="," close=")">
            #{expenseId}
        </foreach>
    </delete>

    <select id="selectLastFuelExpense" parameterType="Long" resultMap="VeExpenseResult">
        <include refid="selectVeExpenseVo"/>
        where e.car_id = #{carId} and e.type = 0 and e.is_deleted = 0
        order by e.date desc limit 1
    </select>

    <select id="selectLastFuelExpenseBeforeDate" resultMap="VeExpenseResult">
        <include refid="selectVeExpenseVo"/>
        where e.car_id = #{carId} and e.type = 0 and e.date &lt; #{date} and e.is_deleted = 0
        order by e.date desc limit 1
    </select>

    <select id="selectVeExpenseById" parameterType="Long" resultMap="VeExpenseResult">
        <include refid="selectVeExpenseVo"/>
        where e.expense_id = #{expenseId} and e.is_deleted = 0
    </select>
</mapper>
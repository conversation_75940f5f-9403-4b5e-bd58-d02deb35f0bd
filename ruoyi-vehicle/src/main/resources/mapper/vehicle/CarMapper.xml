<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vehicle.mapper.CarMapper">

    <!-- 定义结果映射 -->
    <resultMap type="Car" id="CarResult">
        <result property="carId" column="car_id" />
        <result property="brandId" column="brand_id" />
        <result property="brand" column="brand_name" />
        <result property="modelId" column="model_id" />
        <result property="model" column="model_name" />
        <result property="fuelType" column="fuel_type" />
        <result property="euroCategory" column="euro_category" />
        <result property="licensePlate" column="license_plate" />
        <result property="totalMileage" column="total_mileage" />
        <result property="subtotalMileage" column="subtotal_mileage" />
        <result property="hasEtc" column="has_etc" />
        <result property="carModelImage" column="car_model_image" />
        <result property="userId" column="user_id" />
        <result property="userName" column="user_name" />
        <result property="color" column="color" />
        <result property="status" column="status" />
        <result property="brandLogo" column="brand_logo" />
        <result property="delFlag" column="del_flag" />
        <result property="totalDrivingTime" column="total_driving_time" />
        <result property="lastMileageCalibration" column="last_mileage_calibration" />
    </resultMap>


    <!-- 定义一个查询所有car表的SQL片段 -->
    <sql id="selectCarVo">
        select c.car_id, 
               c.brand_id,
               b.name as brand_name,
               b.logo as brand_logo,
               c.model_id,
               m.name as model_name,
               c.fuel_type,
               c.euro_category,
               c.license_plate,
               c.total_mileage,
               c.subtotal_mileage,
               c.has_etc,
               c.car_model_image,
               c.user_id,
               u.nick_name as user_name,
               c.color,
               c.status,
               c.del_flag,
               c.total_driving_time,
               c.last_mileage_calibration,
               c.create_by,
               c.create_time,
               c.update_by,
               c.update_time
        from car c
        left join brands b on c.brand_id = b.id
        left join models m on c.model_id = m.id
        left join sys_user u on c.user_id = u.user_id
    </sql>

    <!-- 查询满足条件的车辆列表 -->
    <select id="selectCarList" parameterType="Car" resultMap="CarResult">
        <include refid="selectCarVo"/>
        <where>
            and c.del_flag = 0
            <if test="brand != null and brand != ''">
                AND b.name like concat('%', #{brand}, '%')
            </if>
            <if test="model != null and model != ''">
                AND m.name like concat('%', #{model}, '%')
            </if>
            <if test="licensePlate != null and licensePlate != ''">
                AND c.license_plate = #{licensePlate}
            </if>
            <if test="userId != null">
                AND c.user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                AND u.nick_name like concat('%', #{userName}, '%')
            </if>
            ${params.dataScope}
        </where>
    </select>

    <!-- 根据车辆ID查询车辆信息 -->
    <select id="selectCarByCarId" parameterType="Long" resultMap="CarResult">
        <include refid="selectCarVo"/>
        where car_id = #{carId} and c.del_flag = 0
    </select>

    <!-- 插入车辆信息 -->
    <insert id="insertCar" parameterType="Car" useGeneratedKeys="true" keyProperty="carId">
        insert into car
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brandId != null">brand_id,</if>
            <if test="modelId != null">model_id,</if>
            <if test="fuelType != null">fuel_type,</if>
            <if test="euroCategory != null">euro_category,</if>
            <if test="licensePlate != null and licensePlate != ''">license_plate,</if>
            <if test="totalMileage != null">total_mileage,</if>
            <if test="subtotalMileage != null">subtotal_mileage,</if>
            <if test="hasEtc != null">has_etc,</if>
            <if test="carModelImage != null">car_model_image,</if>
            <if test="userId != null">user_id,</if>
            <if test="color != null">color,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brandId != null">#{brandId},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="fuelType != null">#{fuelType},</if>
            <if test="euroCategory != null">#{euroCategory},</if>
            <if test="licensePlate != null and licensePlate != ''">#{licensePlate},</if>
            <if test="totalMileage != null">#{totalMileage},</if>
            <if test="subtotalMileage != null">#{subtotalMileage},</if>
            <if test="hasEtc != null">#{hasEtc},</if>
            <if test="carModelImage != null">#{carModelImage},</if>
            <if test="userId != null">#{userId},</if>
            <if test="color != null">#{color},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <!-- 更新车辆信息 -->
    <update id="updateCar" parameterType="Car">
        update car
        <trim prefix="SET" suffixOverrides=",">
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="fuelType != null">fuel_type = #{fuelType},</if>
            <if test="euroCategory != null">euro_category = #{euroCategory},</if>
            <if test="licensePlate != null and licensePlate != ''">license_plate = #{licensePlate},</if>
            <if test="totalMileage != null">total_mileage = #{totalMileage},</if>
            <if test="subtotalMileage != null">subtotal_mileage = #{subtotalMileage},</if>
            <if test="hasEtc != null">has_etc = #{hasEtc},</if>
            <if test="carModelImage != null">car_model_image = #{carModelImage},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="color != null">color = #{color},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="totalDrivingTime != null">total_driving_time = #{totalDrivingTime},</if>
            <if test="lastMileageCalibration != null">last_mileage_calibration = #{lastMileageCalibration},</if>
            update_time = sysdate()
        </trim>
        where car_id = #{carId}
    </update>

    <!-- 删除车辆信息（软删除） -->
    <update id="deleteCarByCarId" parameterType="Long">
        update car set del_flag = 1 where car_id = #{carId}
    </update>

    <!-- 批量删除车辆信息（软删除） -->
    <update id="deleteCarByCarIds" parameterType="String">
        update car set del_flag = 1 where car_id in
        <foreach item="carId" collection="array" open="(" separator="," close=")">
            #{carId}
        </foreach>
    </update>

    <!-- 将用户的所有车辆设置为维修中状态 -->
    <update id="updateAllUserCarsToMaintenance">
        update car
        set status = '1'  <!-- 假设1表示维修中状态 -->
        where user_id = #{userId}
    </update>

    <!-- 更新车辆状态 -->
    <update id="updateCarStatus">
        update car
        set status = #{status}
        where car_id = #{carId}
    </update>

    <!-- 查询用户当前在用车辆 -->
    <select id="selectInUseCarByUserId" resultMap="CarResult">
        <include refid="selectCarVo"/>
        where c.user_id = #{userId} and c.status = '0'  <!-- 假设0表示在用状态 -->
        limit 1
    </select>
</mapper>
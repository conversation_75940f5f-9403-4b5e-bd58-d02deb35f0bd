<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vehicle.mapper.CarMileageLogMapper">
    
    <resultMap type="CarMileageLog" id="CarMileageLogResult">
        <result property="logId"    column="log_id"    />
        <result property="carId"    column="car_id"    />
        <result property="originalMileage"    column="original_mileage"    />
        <result property="newMileage"    column="new_mileage"    />
        <result property="adjustment"    column="adjustment"    />
        <result property="reason"    column="reason"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectCarMileageLogVo">
        select log_id, car_id, original_mileage, new_mileage, adjustment, reason, create_by, create_time
        from car_mileage_log
    </sql>

    <select id="selectCarMileageLogList" parameterType="CarMileageLog" resultMap="CarMileageLogResult">
        <include refid="selectCarMileageLogVo"/>
        <where>
            <if test="carId != null "> and car_id = #{carId}</if>
        </where>
        order by create_time desc
    </select>

    <insert id="insertCarMileageLog" parameterType="CarMileageLog" useGeneratedKeys="true" keyProperty="logId">
        insert into car_mileage_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="carId != null">car_id,</if>
            <if test="originalMileage != null">original_mileage,</if>
            <if test="newMileage != null">new_mileage,</if>
            <if test="adjustment != null">adjustment,</if>
            <if test="reason != null">reason,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="carId != null">#{carId},</if>
            <if test="originalMileage != null">#{originalMileage},</if>
            <if test="newMileage != null">#{newMileage},</if>
            <if test="adjustment != null">#{adjustment},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

</mapper> 
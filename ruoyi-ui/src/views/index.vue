<template>
  <div class="dashboard-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <i class="el-icon-user"></i>
              <span>今日新增用户</span>
            </div>
            <div class="stat-value">{{ stats.todayNewUsers }}</div>
          </div>
          <div class="stat-footer">
            <span>总用户</span>
            <span class="total">{{ stats.totalUsers }}人</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <i class="el-icon-s-custom"></i>
              <span>昨日活跃用户</span>
            </div>
            <div class="stat-value">{{ stats.yesterdayActiveUsers }}</div>
          </div>
          <div class="stat-footer">
            <span>较前日</span>
            <span :class="stats.activeIncrease >= 0 ? 'up' : 'down'">
              {{ Math.abs(stats.activeIncrease) }}%
              <i :class="stats.activeIncrease >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
            </span>
          </div>
        </el-card>
      </el-col>
          <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <i class="el-icon-user-solid"></i>
              <span>当前在线用户</span>
            </div>
            <div class="stat-value">{{ stats.onlineUsers }}</div>
          </div>
          <div class="stat-footer">
            <span>访问IP数</span>
            <span class="total">{{ stats.visitIpCount }}个</span>
          </div>
        </el-card>
          </el-col>
          <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <i class="el-icon-view"></i>
              <span>系统访问总量</span>
            </div>
            <div class="stat-value">{{ stats.totalVisits }}</div>
          </div>
          <div class="stat-footer">
            <span>今日访问</span>
            <span class="total">{{ stats.todayVisits }}次</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="16">
        <el-card>
          <div slot="header" class="clearfix">
            <span>营业额趋势</span>
            <el-radio-group v-model="chartTimeRange" size="small" style="float: right">
              <el-radio-button label="week">本周</el-radio-button>
              <el-radio-button label="month">本月</el-radio-button>
              <el-radio-button label="year">全年</el-radio-button>
            </el-radio-group>
          </div>
          <div ref="revenueChart" style="height: 350px"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <div slot="header" class="clearfix">
            <span>用户分布</span>
          </div>
          <div ref="userDistChart" style="height: 350px"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 待办事项和通知 -->
    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>待办事项</span>
            <el-button style="float: right; padding: 3px 0" type="text">查看更多</el-button>
          </div>
          <el-table :data="todos" style="width: 100%" :show-header="false">
            <el-table-column width="40">
              <template slot-scope="scope">
                <el-checkbox v-model="scope.row.done"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column prop="content">
              <template slot-scope="scope">
                <span :class="{ 'todo-done': scope.row.done }">{{ scope.row.content }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="deadline" width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.deadline }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>系统通知</span>
            <el-button style="float: right; padding: 3px 0" type="text">查看更多</el-button>
          </div>
          <div v-for="notice in notices" :key="notice.id" class="notice-item">
            <div class="notice-title">
              <span>{{ notice.title }}</span>
              <span class="notice-time">{{ notice.time }}</span>
            </div>
            <div class="notice-content">{{ notice.content }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="24">
        <el-card>
          <div slot="header" class="clearfix">
            <span>快捷操作</span>
          </div>
          <div class="quick-actions">
            <el-button type="primary" icon="el-icon-plus" @click="handleQuickAction('add')">新增订单</el-button>
            <el-button type="success" icon="el-icon-user" @click="handleQuickAction('user')">用户管理</el-button>
            <el-button type="warning" icon="el-icon-goods" @click="handleQuickAction('product')">商品管理</el-button>
            <el-button type="danger" icon="el-icon-document" @click="handleQuickAction('report')">生成报表</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getOnlineUserList } from "@/api/monitor/online"; // 引入在线用户接口
import { listLogininfor } from "@/api/monitor/logininfor"; // 引入登录日志接口
import { listUser } from "@/api/system/user"; // 引入用户列表接口

export default {
  name: "Index",
  data() {
    return {
      // 统计数据
      stats: {
        todayNewUsers: 0,
        totalUsers: 0,
        yesterdayActiveUsers: 0,
        activeIncrease: 0,
        onlineUsers: 0,
        visitIpCount: 0,
        totalVisits: 0,
        todayVisits: 0
      },
      // 图表时间范围
      chartTimeRange: 'week',
      // 待办事项
      todos: [
        { content: '审核新用户注册申请', deadline: '今天 14:00', done: false },
        { content: '处理退款申请', deadline: '今天 16:00', done: false },
        { content: '更新系统版本', deadline: '明天 10:00', done: false },
        { content: '回复用户反馈', deadline: '明天 14:00', done: true },
        { content: '检查服务器状态', deadline: '已过期', done: false }
      ],
      // 系统通知
      notices: [
        {
          id: 1,
          title: '系统更新通知',
          content: '系统将于今晚22:00进行例行维护更新，预计持续1小时',
          time: '10分钟前'
        },
        {
          id: 2,
          title: '安全预警',
          content: '检测到异常登录行为，请及时查看系统日志',
          time: '1小时前'
        },
        {
          id: 3,
          title: '待处理订单提醒',
          content: '有3个订单待处理，请及时确认',
          time: '2小时前'
        }
      ]
    }
  },
  created() {
    this.getStats()
  },
  mounted() {
    this.initRevenueChart()
    this.initUserDistChart()
  },
  methods: {
    async getStats() {
      try {
        // 获取在线用户数
        const onlineRes = await getOnlineUserList()
        this.stats.onlineUsers = onlineRes.rows ? onlineRes.rows.length : 0

        // 获取访问统计
        const loginforParams = {
          pageSize: 10000,  // 设置较大的数值以获取所有记录
          pageNum: 1,
          status: "0"  // 成功的登录记录
        }
        const visitRes = await listLogininfor(loginforParams)
        this.stats.totalVisits = visitRes.total || 0

        // 获取今日访问量
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        const todayStr = this.parseTime(today, '{y}-{m}-{d}')
        loginforParams.params = {
          loginTime: todayStr
        }
        const todayVisitRes = await listLogininfor(loginforParams)
        this.stats.todayVisits = todayVisitRes.total || 0

        // 获取用户统计
        const userParams = {
          pageSize: 10000,
          pageNum: 1,
          status: "0"  // 正常状态的用户
        }
        const userRes = await listUser(userParams)
        this.stats.totalUsers = userRes.total || 0

        // 计算今日新增用户
        const todayUserParams = {
          ...userParams,
          params: {
            createTime: todayStr
          }
        }
        const todayUserRes = await listUser(todayUserParams)
        this.stats.todayNewUsers = todayUserRes.total || 0

        // 获取昨日活跃用户
        const yesterday = new Date(today)
        yesterday.setDate(yesterday.getDate() - 1)
        const yesterdayStr = this.parseTime(yesterday, '{y}-{m}-{d}')
        loginforParams.params = {
          loginTime: yesterdayStr
        }
        const yesterdayLoginRes = await listLogininfor(loginforParams)
        // 统计去重的用户数
        const uniqueUsers = new Set(yesterdayLoginRes.rows?.map(item => item.userName) || [])
        this.stats.yesterdayActiveUsers = uniqueUsers.size

        // 计算活跃用户增长率
        const beforeYesterday = new Date(yesterday)
        beforeYesterday.setDate(beforeYesterday.getDate() - 1)
        const beforeYesterdayStr = this.parseTime(beforeYesterday, '{y}-{m}-{d}')
        loginforParams.params = {
          loginTime: beforeYesterdayStr
        }
        const beforeYesterdayLoginRes = await listLogininfor(loginforParams)
        const beforeUniqueUsers = new Set(beforeYesterdayLoginRes.rows?.map(item => item.userName) || [])
        const beforeCount = beforeUniqueUsers.size

        if (beforeCount === 0) {
          this.stats.activeIncrease = 100
        } else {
          this.stats.activeIncrease = ((uniqueUsers.size - beforeCount) / beforeCount * 100).toFixed(1)
        }

        // 访问IP统计
        const uniqueIPs = new Set(visitRes.rows?.map(item => item.ipaddr) || [])
        this.stats.visitIpCount = uniqueIPs.size

      } catch (error) {
        console.error("获取统计数据失败", error)
      }
    },
    // 添加时间格式化方法
    parseTime(time, pattern) {
      if (arguments.length === 0 || !time) {
        return null
      }
      const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
      let date
      if (typeof time === 'object') {
        date = time
      } else {
        if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
          time = parseInt(time)
        } else if (typeof time === 'string') {
          time = time.replace(new RegExp(/-/gm), '/')
        }
        if ((typeof time === 'number') && (time.toString().length === 10)) {
          time = time * 1000
        }
        date = new Date(time)
      }
      const formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay()
      }
      const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
        let value = formatObj[key]
        // Note: getDay() returns 0 on Sunday
        if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
        if (result.length > 0 && value < 10) {
          value = '0' + value
        }
        return value || 0
      })
      return time_str
    },
    // 初始化营收趋势图表
    initRevenueChart() {
      const chart = echarts.init(this.$refs.revenueChart)
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '营业额',
            type: 'line',
            smooth: true,
            data: [12000, 13200, 14100, 13800, 15600, 16800, 18000],
            areaStyle: {
              opacity: 0.1
            },
            lineStyle: {
              width: 3
            }
          }
        ]
      }
      chart.setOption(option)
    },
    // 初始化用户分布图表
    initUserDistChart() {
      const chart = echarts.init(this.$refs.userDistChart)
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '用户分布',
            type: 'pie',
            radius: '50%',
            data: [
              { value: 1048, name: '华东地区' },
              { value: 735, name: '华北地区' },
              { value: 580, name: '华南地区' },
              { value: 484, name: '西南地区' },
              { value: 300, name: '其他地区' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      chart.setOption(option)
    },
    // 处理快捷操作
    handleQuickAction(action) {
      switch (action) {
        case 'add':
          this.$router.push('/order/add')
          break
        case 'user':
          this.$router.push('/system/user')
          break
        case 'product':
          this.$router.push('/product/list')
          break
        case 'report':
          this.$message.success('正在生成报表，请稍候...')
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f0f2f5;

  .stat-card {
    .stat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    margin-bottom: 20px;

      .stat-title {
        color: #666;
        font-size: 14px;
        i {
          margin-right: 5px;
        }
      }

      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
      }
    }

    .stat-footer {
      font-size: 12px;
      color: #909399;

      .up {
        color: #67c23a;
        margin-left: 5px;
      }

      .down {
        color: #f56c6c;
        margin-left: 5px;
      }

      .total {
        color: #409EFF;
        margin-left: 5px;
        font-weight: bold;
      }
    }
  }

  .notice-item {
    padding: 10px 0;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }

    .notice-title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;

      span {
        font-weight: bold;
      }

      .notice-time {
        font-size: 12px;
        color: #909399;
        font-weight: normal;
      }
    }

    .notice-content {
      color: #606266;
      font-size: 13px;
    }
  }

  .todo-done {
    text-decoration: line-through;
    color: #909399;
  }

  .quick-actions {
    display: flex;
    gap: 15px;

    .el-button {
      padding: 15px 25px;
    }
  }
}
</style>


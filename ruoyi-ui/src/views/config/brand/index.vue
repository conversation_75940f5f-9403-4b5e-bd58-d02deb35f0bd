<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="品牌名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入品牌名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="首字母" prop="letter">
        <el-input v-model="queryParams.letter" placeholder="请输入首字母" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="热门品牌" prop="isHot">
        <el-select v-model="queryParams.isHot" placeholder="是否热门" clearable>
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="品牌状态" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['config:brand:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['config:brand:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['config:brand:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['config:brand:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="brandList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="品牌名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="品牌Logo" align="center" width="120">
        <template slot-scope="scope">
          <el-image 
            v-if="scope.row.logo"
            style="width: 50px; height: 50px; display: inline-block;"
            :src="getProxyImageUrl(scope.row.logo)"
            :preview-src-list="scope.row.logo ? [getProxyImageUrl(scope.row.logo)] : []"
            fit="scale-down">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
          <div v-else class="image-slot">
            <i class="el-icon-picture-outline"></i>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="首字母" align="center" prop="letter" width="80" />
      <el-table-column label="热门品牌" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isHot ? 'success' : 'info'">
            {{ scope.row.isHot ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="hotIndex" width="80" />
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['config:brand:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['config:brand:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改品牌目录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="品牌名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入品牌名称" />
        </el-form-item>
        <el-form-item label="品牌Logo" prop="logo">
          <el-upload
            class="avatar-uploader"
            :action="upload.url"
            :headers="upload.headers"
            :show-file-list="false"
            :on-success="handleLogoSuccess"
            :before-upload="beforeLogoUpload">
            <img v-if="form.logo" :src="getProxyImageUrl(form.logo)" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="Logo描述" prop="logoText">
          <el-input v-model="form.logoText" placeholder="请输入Logo描述" />
        </el-form-item>
        <el-form-item label="首字母" prop="letter">
          <el-input v-model="form.letter" placeholder="请输入首字母" maxlength="1" />
        </el-form-item>
        <el-form-item label="是否热门" prop="isHot">
          <el-switch v-model="form.isHot" />
        </el-form-item>
        <el-form-item label="热门排序" prop="hotIndex" v-if="form.isHot">
          <el-input-number v-model="form.hotIndex" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-divider content-position="center">车型信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddVehicleModel">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteVehicleModel">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="vehicleModelList" :row-class-name="rowVehicleModelIndex" @selection-change="handleVehicleModelSelectionChange" ref="vehicleModel">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="车型名称" prop="name" width="200">
            <template slot-scope="scope">
              <el-input v-model="scope.row.name" placeholder="请输入车型名称" />
            </template>
          </el-table-column>
          <el-table-column label="车型图片" align="center" width="120">
            <template slot-scope="scope">
              <el-upload
                class="avatar-uploader"
                :action="upload.url"
                :headers="upload.headers"
                :show-file-list="false"
                :on-success="(res) => handleModelImageSuccess(res, scope.row)"
                :before-upload="beforeImageUpload">
                <img v-if="scope.row.image" :src="getProxyImageUrl(scope.row.image)" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </template>
          </el-table-column>
          <el-table-column label="新车型" width="80">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.isNew" />
            </template>
          </el-table-column>
          <el-table-column label="热门" width="80">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.isHot" />
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <el-select v-model="scope.row.status" placeholder="请选择">
                <el-option :value="1" label="启用" />
                <el-option :value="0" label="禁用" />
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBrand, getBrand, delBrand, addBrand, updateBrand } from "@/api/config/brand";
import { getToken } from "@/utils/auth";
import DictTag from "@/components/DictTag";

export default {
  name: "Brand",
  components: {
    DictTag
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedVehicleModel: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 品牌目录表格数据
      brandList: [],
      // ${subTable.functionName}表格数据
      vehicleModelList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        letter: undefined,
        isHot: undefined,
        status: undefined
      },
      // 表单参数
      form: {
        id: undefined,
        name: undefined,
        logo: undefined,
        logoText: undefined,
        letter: undefined,
        isHot: false,
        hotIndex: 0,
        status: 1,
        modelList: []
      },
      // 表单校验规则
      rules: {
        name: [
          { required: true, message: "品牌名称不能为空", trigger: "blur" }
        ],
        letter: [
          { required: true, message: "首字母不能为空", trigger: "blur" },
          { pattern: /^[A-Z]$/, message: "请输入大写字母", trigger: "blur" }
        ],
        logo: [
          { required: true, message: "品牌Logo不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      },
      // 文件上传配置
      upload: {
        url: process.env.VUE_APP_BASE_API + "/common/upload",
        headers: { Authorization: "Bearer " + getToken() }
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询品牌目录列表 */
    getList() {
      this.loading = true;
      listBrand(this.queryParams).then(response => {
        console.log('Brand list response:', response);
        if (response && response.rows) {
          this.brandList = response.rows.map(item => ({
            ...item,
            logo: item.logo || '',
            // 预处理图片URL
            proxyLogo: this.getProxyImageUrl(item.logo)
          }));
          console.log('Processed brandList:', this.brandList);
          this.total = response.total;
        } else {
          this.$modal.msgError("获取数据格式错误");
        }
      }).catch(error => {
        console.error('Failed to fetch brand list:', error);
        this.$modal.msgError("获取品牌列表失败");
      }).finally(() => {
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        logo: null,
        logoText: null,
        letter: null,
        isHot: false,
        hotIndex: 0,
        status: 1,
        modelList: []
      };
      this.vehicleModelList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加品牌目录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBrand(id).then(response => {
        if (response.code === 200 && response.data) {
          this.form = response.data;
          this.vehicleModelList = response.data.modelList || [];
          this.open = true;
          this.title = "修改品牌目录";
        } else {
          this.$modal.msgError(response.msg || "获取详情失败");
        }
      }).catch(error => {
        console.error('Failed to fetch brand detail:', error);
        this.$modal.msgError("获取详情失败");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.modelList = this.vehicleModelList.map(item => ({
            ...item,
            brandId: this.form.id
          }));
          
          if (this.form.id != null) {
            updateBrand(this.form).then(response => {
              if (response.code === 200) {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              } else {
                this.$modal.msgError(response.msg || "修改失败");
              }
            }).catch(() => {
              this.$modal.msgError("修改失败");
            });
          } else {
            addBrand(this.form).then(response => {
              if (response.code === 200) {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.$modal.msgError(response.msg || "新增失败");
              }
            }).catch(() => {
              this.$modal.msgError("新增失败");
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除品牌目录编号为"' + ids + '"的数据项？').then(function() {
        return delBrand(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** ${subTable.functionName}序号 */
    rowVehicleModelIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** ${subTable.functionName}添加按钮操作 */
    handleAddVehicleModel() {
      let obj = {
        name: "",
        image: "",
        isNew: false,
        isHot: false,
        status: 1
      };
      this.vehicleModelList.push(obj);
    },
    /** ${subTable.functionName}删除按钮操作 */
    handleDeleteVehicleModel() {
      if (this.checkedVehicleModel.length == 0) {
        this.$modal.msgError("请先选择要删除的${subTable.functionName}数据");
      } else {
        const vehicleModelList = this.vehicleModelList;
        const checkedVehicleModel = this.checkedVehicleModel;
        this.vehicleModelList = vehicleModelList.filter(function(item) {
          return checkedVehicleModel.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleVehicleModelSelectionChange(selection) {
      this.checkedVehicleModel = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('config/brand/export', {
        ...this.queryParams
      }, `brand_${new Date().getTime()}.xlsx`)
    },
    // Logo上传成功处理
    handleLogoSuccess(response, file) {
      this.form.logo = response.url.startsWith('http') ? response.url : process.env.VUE_APP_BASE_API + response.url;
    },
    // Logo上传前的校验
    beforeLogoUpload(file) {
      const isImage = file.type.indexOf('image/') === 0;
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$message.error('上传Logo图片只能是图片格式!');
        return false;
      }
      if (!isLt2M) {
        this.$message.error('上传Logo图片大小不能超过 2MB!');
        return false;
      }
      return true;
    },
    // 添加处理车型图片上传的方法
    handleModelImageSuccess(response, row) {
      row.image = response.url.startsWith('http') ? response.url : process.env.VUE_APP_BASE_API + response.url;
    },
    // 添加新车型时的默认值
    handleAddVehicleModel() {
      let obj = {
        name: "",
        image: "",
        isNew: false,
        isHot: false,
        status: 1
      };
      this.vehicleModelList.push(obj);
    },
    beforeImageUpload(file) {
      const isImage = file.type.indexOf('image/') === 0;
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$message.error('只能上传图片文件!');
        return false;
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!');
        return false;
      }
      return true;
    },
    // 添加图片代理方法
    getProxyImageUrl(url) {
      if (!url) return '';
      return `https://images.weserv.nl/?url=${encodeURIComponent(url)}`;
    }
  }
};
</script>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: contain;
  background-color: #fff;
}

/* 车型图片上传样式 */
.el-table .avatar-uploader .el-upload {
  width: 50px;
  height: 50px;
}
.el-table .avatar-uploader .avatar {
  width: 50px;
  height: 50px;
}
.el-table .avatar-uploader .avatar-uploader-icon {
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: 20px;
}
</style>

<style scoped>
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  margin: 0 auto;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.el-image {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 2px;
  background-color: #fff;
  margin: 0 auto;
}

/* 确保图片容器居中显示 */
.el-table .cell {
  text-align: center;
}

/* 图片显示优化 */
.el-image ::v-deep img {
  object-fit: contain;
  width: 100%;
  height: 100%;
}
</style>

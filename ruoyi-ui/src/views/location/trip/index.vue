<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车辆ID" prop="carId">
        <el-input
          v-model="queryParams.carId"
          placeholder="请输入车辆ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker clearable
          v-model="queryParams.startTime"
          type="date"
          value-format="timestamp"
          placeholder="请选择开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker clearable
          v-model="queryParams.endTime"
          type="date"
          value-format="timestamp"
          placeholder="请选择结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['location:trip:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['location:trip:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['location:trip:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['location:trip:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tripList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="行程ID" align="center" prop="id" />
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总距离(米)" align="center" prop="totalDistance" />
      <el-table-column label="总时长(秒)" align="center" prop="totalTime" />
      <el-table-column label="运动时长(秒)" align="center" prop="activeTime" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['location:trip:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['location:trip:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改行程管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker clearable
            v-model="form.startTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker clearable
            v-model="form.endTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="总距离(米)" prop="totalDistance">
          <el-input v-model="form.totalDistance" placeholder="请输入总距离(米)" />
        </el-form-item>
        <el-form-item label="总时长(秒)" prop="totalTime">
          <el-input v-model="form.totalTime" placeholder="请输入总时长(秒)" />
        </el-form-item>
        <el-form-item label="运动时长(秒)" prop="activeTime">
          <el-input v-model="form.activeTime" placeholder="请输入运动时长(秒)" />
        </el-form-item>
        <el-form-item label="最高海拔(米)" prop="maxAltitude">
          <el-input v-model="form.maxAltitude" placeholder="请输入最高海拔(米)" />
        </el-form-item>
        <el-form-item label="最低海拔(米)" prop="minAltitude">
          <el-input v-model="form.minAltitude" placeholder="请输入最低海拔(米)" />
        </el-form-item>
        <el-form-item label="平均海拔(米)" prop="avgAltitude">
          <el-input v-model="form.avgAltitude" placeholder="请输入平均海拔(米)" />
        </el-form-item>
        <el-form-item label="最大速度(米/秒)" prop="maxSpeed">
          <el-input v-model="form.maxSpeed" placeholder="请输入最大速度(米/秒)" />
        </el-form-item>
        <el-form-item label="平均速度(米/秒)" prop="avgSpeed">
          <el-input v-model="form.avgSpeed" placeholder="请输入平均速度(米/秒)" />
        </el-form-item>
        <el-form-item label="最大加速度(米/秒²)" prop="maxAcceleration">
          <el-input v-model="form.maxAcceleration" placeholder="请输入最大加速度(米/秒²)" />
        </el-form-item>
        <el-form-item label="起点纬度" prop="startLatitude">
          <el-input v-model="form.startLatitude" placeholder="请输入起点纬度" />
        </el-form-item>
        <el-form-item label="起点经度" prop="startLongitude">
          <el-input v-model="form.startLongitude" placeholder="请输入起点经度" />
        </el-form-item>
        <el-form-item label="终点纬度" prop="endLatitude">
          <el-input v-model="form.endLatitude" placeholder="请输入终点纬度" />
        </el-form-item>
        <el-form-item label="终点经度" prop="endLongitude">
          <el-input v-model="form.endLongitude" placeholder="请输入终点经度" />
        </el-form-item>
        <el-divider content-position="center">行程轨迹点信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddLocationTripDetail">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteLocationTripDetail">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="locationTripDetailList" :row-class-name="rowLocationTripDetailIndex" @selection-change="handleLocationTripDetailSelectionChange" ref="locationTripDetail">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="纬度" prop="latitude" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.latitude" placeholder="请输入纬度" />
            </template>
          </el-table-column>
          <el-table-column label="经度" prop="longitude" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.longitude" placeholder="请输入经度" />
            </template>
          </el-table-column>
          <el-table-column label="时间戳" prop="timestamp" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.timestamp" placeholder="请输入时间戳" />
            </template>
          </el-table-column>
          <el-table-column label="精度(米)" prop="accuracy" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.accuracy" placeholder="请输入精度(米)" />
            </template>
          </el-table-column>
          <el-table-column label="海拔(米)" prop="altitude" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.altitude" placeholder="请输入海拔(米)" />
            </template>
          </el-table-column>
          <el-table-column label="速度(米/秒)" prop="speed" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.speed" placeholder="请输入速度(米/秒)" />
            </template>
          </el-table-column>
          <el-table-column label="方向(度)" prop="bearing" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.bearing" placeholder="请输入方向(度)" />
            </template>
          </el-table-column>
          <el-table-column label="卫星数" prop="satellites" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.satellites" placeholder="请输入卫星数" />
            </template>
          </el-table-column>
          <el-table-column label="定位类型" prop="provider" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.provider" placeholder="请输入定位类型" />
            </template>
          </el-table-column>
          <el-table-column label="定位提供者" prop="locationProvider" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.locationProvider" placeholder="请输入定位提供者" />
            </template>
          </el-table-column>
          <el-table-column label="是否已上传" prop="isUploaded" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.isUploaded" placeholder="请输入是否已上传" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTrip, getTrip, delTrip, addTrip, updateTrip } from "@/api/location/trip";

export default {
  name: "Trip",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedLocationTripDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 行程管理表格数据
      tripList: [],
      // 行程轨迹点表格数据
      locationTripDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        carId: null,
        startTime: null,
        endTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询行程管理列表 */
    getList() {
      this.loading = true;
      listTrip(this.queryParams).then(response => {
        this.tripList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        carId: null,
        localId: null,
        type: null,
        startTime: null,
        endTime: null,
        totalDistance: null,
        totalTime: null,
        activeTime: null,
        maxAltitude: null,
        minAltitude: null,
        avgAltitude: null,
        maxSpeed: null,
        avgSpeed: null,
        maxAcceleration: null,
        startLatitude: null,
        startLongitude: null,
        endLatitude: null,
        endLongitude: null,
        createTime: null,
        updateTime: null
      };
      this.locationTripDetailList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 转换开始时间
      if (this.queryParams.startTime) {
        const startDate = new Date(this.queryParams.startTime);
        this.queryParams.startTime = startDate.getTime();
      }
      // 转换结束时间
      if (this.queryParams.endTime) {
        const endDate = new Date(this.queryParams.endTime);
        endDate.setHours(23, 59, 59, 999);
        this.queryParams.endTime = endDate.getTime();
      }
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加行程管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getTrip(id).then(response => {
        this.form = response.data;
        this.locationTripDetailList = response.data.locationTripDetailList;
        this.open = true;
        this.title = "修改行程管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.locationTripDetailList = this.locationTripDetailList;
          if (this.form.id != null) {
            updateTrip(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTrip(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除行程管理编号为"' + ids + '"的数据项？').then(function() {
        return delTrip(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** 行程轨迹点序号 */
    rowLocationTripDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 行程轨迹点添加按钮操作 */
    handleAddLocationTripDetail() {
      let obj = {};
      obj.latitude = "";
      obj.longitude = "";
      obj.timestamp = "";
      obj.accuracy = "";
      obj.altitude = "";
      obj.speed = "";
      obj.bearing = "";
      obj.satellites = "";
      obj.provider = "";
      obj.locationProvider = "";
      obj.isUploaded = "";
      this.locationTripDetailList.push(obj);
    },
    /** 行程轨迹点删除按钮操作 */
    handleDeleteLocationTripDetail() {
      if (this.checkedLocationTripDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的行程轨迹点数据");
      } else {
        const locationTripDetailList = this.locationTripDetailList;
        const checkedLocationTripDetail = this.checkedLocationTripDetail;
        this.locationTripDetailList = locationTripDetailList.filter(function(item) {
          return checkedLocationTripDetail.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleLocationTripDetailSelectionChange(selection) {
      this.checkedLocationTripDetail = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('location/trip/export', {
        ...this.queryParams
      }, `trip_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

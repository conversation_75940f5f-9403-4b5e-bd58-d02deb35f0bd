<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="品牌" prop="brand">
        <el-input v-model="queryParams.brand" placeholder="请输入品牌" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="型号" prop="model">
        <el-input v-model="queryParams.model" placeholder="请输入型号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="车牌号" prop="licensePlate">
        <el-input v-model="queryParams.licensePlate" placeholder="请输入车牌号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="用户" prop="userId">
        <el-input v-model="queryParams.userName" placeholder="请输入用户" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="车辆状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择车辆状态" clearable>
          <el-option v-for="dict in dict.type.vehicle_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['vehicle:car:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['vehicle:car:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['vehicle:car:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['vehicle:car:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="carList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="品牌" align="center">
        <template slot-scope="scope">
          <div style="display: flex; align-items: center; justify-content: center;">
            <el-image 
              v-if="scope.row.brandLogo"
              style="width: 40px; height: 40px; margin-right: 8px;"
              :src="getProxyImageUrl(scope.row.brandLogo)"
              :preview-src-list="[getProxyImageUrl(scope.row.brandLogo)]"
              fit="contain">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <span>{{ scope.row.brand }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="型号" align="center" prop="model" />
      <el-table-column label="车牌号" align="center" prop="licensePlate" />
      <el-table-column label="行驶总里程" align="center" prop="totalMileage" />
      <el-table-column label="用户" align="center" prop="userName" />
      <el-table-column label="车辆状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.vehicle_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['vehicle:car:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['vehicle:car:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleSetInUse(scope.row)"
            v-if="scope.row.status !== '0'"
            v-hasPermi="['vehicle:car:edit']"
          >设为在用</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改我的车对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="品牌" prop="brandId">
          <div style="display: flex; align-items: center;">
            <el-input v-model="form.brand" placeholder="请选择品牌" readonly style="flex: 1;">
              <el-button slot="append" icon="el-icon-search" @click="handleSelectBrand"></el-button>
            </el-input>
            <el-image 
              v-if="form.brandLogo"
              style="width: 40px; height: 40px; margin-left: 8px;"
              :src="getProxyImageUrl(form.brandLogo)"
              fit="contain">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </div>
        </el-form-item>
        <el-form-item label="型号" prop="modelId">
          <el-input v-model="form.model" placeholder="请选择型号" readonly>
            <el-button slot="append" icon="el-icon-search" @click="handleSelectModel" :disabled="!form.brandId"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="燃料类型" prop="fuelType">
          <el-select v-model="form.fuelType" placeholder="请选择燃料类型">
            <el-option v-for="dict in dict.type.fuel_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排放等级" prop="euroCategory">
          <el-select v-model="form.euroCategory" placeholder="请选择排放等级">
            <el-option v-for="dict in dict.type.euro_category" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车牌号" prop="licensePlate">
          <el-input v-model="form.licensePlate" placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="行驶总里程" prop="totalMileage">
          <el-input v-model="form.totalMileage" placeholder="请输入行驶总里程" />
        </el-form-item>
        <el-form-item label="小计里程" prop="subtotalMileage">
          <el-input v-model="form.subtotalMileage" placeholder="请输入小计里程" />
        </el-form-item>
        <el-form-item label="是否有ETC" prop="hasEtc">
          <el-radio-group v-model="form.hasEtc">
            <el-radio v-for="dict in dict.type.sys_yes_no" :key="dict.value"
              :label="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="图片" prop="carModelImage">
          <image-upload v-model="form.carModelImage" />
        </el-form-item>
        <el-form-item label="用户" prop="userId">
          <el-input v-model="form.userName" placeholder="请选择用户" readonly>
            <el-button slot="append" icon="el-icon-search" @click="handleSelectUser"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="汽车颜色" prop="color">
          <el-input v-model="form.color" placeholder="请输入汽车颜色" />
        </el-form-item>
        <el-form-item label="车辆状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in dict.type.vehicle_status" :key="dict.value"
              :label="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 品牌选择器 -->
    <el-dialog title="选择品牌" :visible.sync="brandOpen" width="800px" append-to-body>
      <el-form :model="brandQueryParams" ref="brandQueryForm" :inline="true">
        <el-form-item label="品牌名称" prop="name">
          <el-input v-model="brandQueryParams.name" placeholder="请输入品牌名称" clearable size="small" @keyup.enter.native="handleBrandQuery"/>
        </el-form-item>
        <el-form-item label="品牌首字母" prop="letter">
          <el-input v-model="brandQueryParams.letter" placeholder="请输入品牌首字母" clearable size="small" @keyup.enter.native="handleBrandQuery"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleBrandQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetBrandQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-table v-loading="brandLoading" :data="brandOptions" @row-click="handleRowClick" highlight-current-row>
        <el-table-column label="品牌名称" align="center" prop="name" />
        <el-table-column label="品牌首字母" align="center" prop="letter" />
        <el-table-column label="品牌LOGO" align="center" prop="logo">
          <template slot-scope="scope">
            <el-image 
              v-if="scope.row.logo"
              style="width: 50px; height: 50px"
              :src="getProxyImageUrl(scope.row.logo)"
              :preview-src-list="[getProxyImageUrl(scope.row.logo)]"
              fit="contain">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="brandTotal>0" :total="brandTotal" :page.sync="brandQueryParams.pageNum" :limit.sync="brandQueryParams.pageSize" @pagination="getBrandList"/>
    </el-dialog>

    <!-- 型号选择器 -->
    <el-dialog title="选择型号" :visible.sync="modelOpen" width="800px" append-to-body>
      <el-form :model="modelQueryParams" ref="modelQueryForm" :inline="true">
        <el-form-item label="型号名称" prop="name">
          <el-input v-model="modelQueryParams.name" placeholder="请输入型号名称" clearable size="small" @keyup.enter.native="handleModelQuery"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleModelQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetModelQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="modelLoading" :data="modelOptions" @row-click="handleModelRowClick" highlight-current-row>
        <el-table-column label="型号名称" align="center" prop="name" />
        <el-table-column label="型号ID" align="center" prop="id" />
        <el-table-column label="型号图片" align="center" prop="image">
          <template slot-scope="scope">
            <el-image 
              v-if="scope.row.image"
              style="width: 50px; height: 50px"
              :src="getProxyImageUrl(scope.row.image)"
              :preview-src-list="[getProxyImageUrl(scope.row.image)]"
              fit="contain">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 用户选择器 -->
    <el-dialog title="选择用户" :visible.sync="userOpen" width="800px" append-to-body>
      <el-form :model="userQueryParams" ref="userQueryForm" :inline="true">
        <el-form-item label="用户名称" prop="nickName">
          <el-input v-model="userQueryParams.nickName" placeholder="请输入用户名称" clearable size="small" @keyup.enter.native="handleUserQuery"/>
        </el-form-item>
        <el-form-item label="手机号码" prop="phonenumber">
          <el-input v-model="userQueryParams.phonenumber" placeholder="请输入手机号码" clearable size="small" @keyup.enter.native="handleUserQuery"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleUserQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetUserQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="userLoading" :data="userOptions" @row-click="handleUserRowClick" highlight-current-row>
        <el-table-column label="用户名称" align="center" prop="nickName" />
        <el-table-column label="手机号码" align="center" prop="phonenumber" />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="userTotal>0" :total="userTotal" :page.sync="userQueryParams.pageNum" :limit.sync="userQueryParams.pageSize" @pagination="getUserList"/>
    </el-dialog>
  </div>
</template>

<script>
import { listCar, getCar, delCar, addCar, updateCar, setCarInUse, getCurrentInUse } from "@/api/vehicle/car";
import { listBrand, getBrand } from "@/api/config/brand";
import { listModel } from "@/api/config/model";
import { listUser } from "@/api/system/user";

export default {
  name: "Car",
  dicts: ['euro_category', 'vehicle_status', 'sys_yes_no', 'fuel_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 我的车表格数据
      carList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        brand: null,
        model: null,
        licensePlate: null,
        userId: null,
        status: null
      },
      // 表单参数
      form: {
        carId: null,
        brandId: undefined,
        modelId: undefined,
        fuelType: null,
        euroCategory: null,
        licensePlate: null,
        totalMileage: null,
        subtotalMileage: null,
        hasEtc: null,
        carModelImage: null,
        userId: null,
        color: null,
        status: null
      },
      // 表单校验
      rules: {
        brandId: [
          { required: true, message: "请选择品牌", trigger: "change" }
        ],
        modelId: [
          { required: true, message: "请选择型号", trigger: "change" }
        ],
        licensePlate: [
          { required: true, message: "车牌号不能为空", trigger: "blur" }
        ],
        userId: [
          { required: true, message: "用户不能为空", trigger: "blur" }
        ],
      },
      // 品牌选项
      brandOptions: [],
      // 型号选项
      modelOptions: [],
      // 品牌选择器参数
      brandOpen: false,
      brandLoading: false,
      brandTotal: 0,
      brandQueryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        status: undefined
      },
      
      // 型号选择器参数
      modelOpen: false,
      modelLoading: false,
      modelTotal: 0,
      modelQueryParams: {
        pageNum: 1,
        pageSize: 10,
        brandId: undefined,
        name: undefined,
        status: undefined
      },
      
      // 用户选择器参数
      userOpen: false,
      userLoading: false,
      userTotal: 0,
      userOptions: [],
      userQueryParams: {
        pageNum: 1,
        pageSize: 10,
        nickName: undefined,
        phonenumber: undefined
      }
    };
  },
  created() {
    this.getList();
    this.loadBrandOptions();
  },
  methods: {
    /** 查询我的车列表 */
    getList() {
      this.loading = true;
      // 先获取当前在用车辆
      getCurrentInUse().then(response => {
        const currentCar = response.data;
        // 再获取列表
        listCar(this.queryParams).then(response => {
          this.carList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 加载品牌选项 */
    loadBrandOptions() {
      listBrand().then(response => {
        this.brandOptions = response.rows;
      });
    },
    /** 品牌选择框变更 */
    handleBrandChange(value) {
      this.form.modelId = undefined;
      this.modelOptions = [];
      if (value) {
        listModel({ brandId: value }).then(response => {
          this.modelOptions = response.rows;
        });
      }
    },
    // 表单重置
    reset() {
      this.form = {
        carId: null,
        brandId: undefined,
        modelId: undefined,
        fuelType: null,
        euroCategory: null,
        licensePlate: null,
        totalMileage: null,
        subtotalMileage: null,
        hasEtc: null,
        carModelImage: null,
        userId: null,
        color: null,
        status: null
      };
      this.modelOptions = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.carId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加我的车";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const carId = row.carId || this.ids
      getCar(carId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改我的车";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.carId != null) {
            updateCar(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCar(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const carIds = row.carId || this.ids;
      this.$modal.confirm('是否确认删除我的车编号为"' + carIds + '"的数据项？').then(function () {
        return delCar(carIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('vehicle/car/export', {
        ...this.queryParams
      }, `car_${new Date().getTime()}.xlsx`)
    },
    /** 打开品牌选择器 */
    handleSelectBrand() {
      this.getBrandList();
      this.brandOpen = true;
    },
    
    /** 获取品牌列表 */
    getBrandList() {
      this.brandLoading = true;
      listBrand(this.brandQueryParams).then(response => {
        this.brandOptions = response.rows;
        this.brandTotal = response.total;
        this.brandLoading = false;
      });
    },
    
    /** 品牌行点击事件 */
    handleRowClick(row) {
      this.form.brandId = row.id;
      this.form.brand = row.name;
      this.form.brandLogo = row.logo;
      // 清空型号
      this.form.modelId = undefined;
      this.form.model = undefined;
      // 获取该品牌下的车型列表
      this.getModelsByBrandId(row.id);
      this.brandOpen = false;
    },
    
    /** 获取品牌下的车型列表 */
    getModelsByBrandId(brandId) {
      this.modelLoading = true;
      getBrand(brandId).then(response => {
        this.modelOptions = response.data.modelList || [];
        this.modelLoading = false;
      });
    },
    
    /** 打开型号选择器 */
    handleSelectModel() {
      if (!this.form.brandId) {
        this.$modal.msgError("请先选择品牌");
        return;
      }
      // 确保已经加载了车型列表
      if (this.modelOptions.length === 0) {
        this.getModelsByBrandId(this.form.brandId);
      }
      this.modelOpen = true;
    },
    
    /** 型号行点击事件 */
    handleModelRowClick(row) {
      this.form.modelId = row.id;
      this.form.model = row.name;
      this.modelOpen = false;
    },
    
    /** 品牌查询按钮操作 */
    handleBrandQuery() {
      this.brandQueryParams.pageNum = 1;
      this.getBrandList();
    },
    
    /** 获取用户列表 */
    getUserList() {
      this.userLoading = true;
      listUser(this.userQueryParams).then(response => {
        this.userOptions = response.rows;
        this.userTotal = response.total;
        this.userLoading = false;
      });
    },
    
    /** 用户行点击事件 */
    handleUserRowClick(row) {
      this.form.userId = row.userId;
      this.form.userName = row.nickName;
      this.userOpen = false;
    },
    
    /** 打开用户选择器 */
    handleSelectUser() {
      this.getUserList();
      this.userOpen = true;
    },
    
    /** 用户查询按钮操作 */
    handleUserQuery() {
      this.userQueryParams.pageNum = 1;
      this.getUserList();
    },
    
    /** 重置用户查询 */
    resetUserQuery() {
      this.resetForm("userQueryForm");
      this.handleUserQuery();
    },
    // 添加图片代理方法
    getProxyImageUrl(url) {
      if (!url) return '';
      return `https://images.weserv.nl/?url=${encodeURIComponent(url)}`;
    },
    /** 设置在用车辆操作 */
    handleSetInUse(row) {
      const carId = row.carId;
      this.$modal.confirm('确认要将该车辆设置为在用状态吗？其他车辆将变为维修中状态。').then(function() {
        return setCarInUse(carId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("设置成功");
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.el-image {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 2px;
  background-color: #fff;
}
</style>

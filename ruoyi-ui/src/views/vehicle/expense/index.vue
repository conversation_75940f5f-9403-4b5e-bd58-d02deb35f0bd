<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="消费类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择消费类型" clearable>
          <el-option
            v-for="dict in dict.type.ve_fuel_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="消费日期" prop="date">
        <el-date-picker clearable
          v-model="queryParams.date"
          type="date"
          value-format="yyyy-MM"
          placeholder="请选择消费日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="车辆" prop="carId">
        <el-input
          v-model="queryParams.carId"
          placeholder="请输入车辆"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['vehicle:expense:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['vehicle:expense:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['vehicle:expense:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['vehicle:expense:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="expenseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="费用ID" align="center" prop="expenseId" />
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="消费类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ve_fuel_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="消费日期" align="center" prop="date" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.date, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="消费金额" align="center" prop="spent" />
      <el-table-column label="费用描述" align="center" prop="description" />
      <el-table-column label="每升价格" align="center" prop="pricePerLiter" />
      <el-table-column label="总公里数" align="center" prop="totalKm" />
      <el-table-column label="用户" align="center" prop="userId" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['vehicle:expense:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['vehicle:expense:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改消费明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="消费类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择消费类型">
            <el-option
              v-for="dict in dict.type.ve_fuel_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="消费日期" prop="date">
          <el-date-picker clearable
            v-model="form.date"
            type="date"
            value-format="yyyy-MM"
            placeholder="请选择消费日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="消费金额" prop="spent">
          <el-input v-model="form.spent" placeholder="请输入消费金额" />
        </el-form-item>
        <el-form-item label="费用描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="每升价格" prop="pricePerLiter">
          <el-input v-model="form.pricePerLiter" placeholder="请输入每升价格" />
        </el-form-item>
        <el-form-item label="总公里数" prop="totalKm">
          <el-input v-model="form.totalKm" placeholder="请输入总公里数" />
        </el-form-item>
        <el-form-item label="车辆" prop="carId">
          <el-input v-model="form.carId" placeholder="请输入车辆" />
        </el-form-item>
        <el-form-item label="用户" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listExpense, getExpense, delExpense, addExpense, updateExpense } from "@/api/vehicle/expense";

export default {
  name: "Expense",
  dicts: ['ve_fuel_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 消费明细表格数据
      expenseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: null,
        date: null,
        carId: null,
        userId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "消费类型不能为空", trigger: "change" }
        ],
        date: [
          { required: true, message: "消费日期不能为空", trigger: "blur" }
        ],
        spent: [
          { required: true, message: "消费金额不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询消费明细列表 */
    getList() {
      this.loading = true;
      listExpense(this.queryParams).then(response => {
        this.expenseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        expenseId: null,
        title: null,
        type: null,
        date: null,
        spent: null,
        description: null,
        pricePerLiter: null,
        totalKm: null,
        carId: null,
        userId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.expenseId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加消费明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const expenseId = row.expenseId || this.ids
      getExpense(expenseId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改消费明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.expenseId != null) {
            updateExpense(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExpense(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const expenseIds = row.expenseId || this.ids;
      this.$modal.confirm('是否确认删除消费明细编号为"' + expenseIds + '"的数据项？').then(function() {
        return delExpense(expenseIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('vehicle/expense/export', {
        ...this.queryParams
      }, `expense_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

import request from '@/utils/request'

// 查询品牌列表
export function listBrand(query) {
  return request({
    url: '/config/brand/list',
    method: 'get',
    params: query
  })
}

// 查询品牌目录详细
export function getBrand(id) {
  return request({
    url: '/config/brand/' + id,
    method: 'get'
  })
}

// 新增品牌目录
export function addBrand(data) {
  return request({
    url: '/config/brand',
    method: 'post',
    data: data
  })
}

// 修改品牌目录
export function updateBrand(data) {
  return request({
    url: '/config/brand',
    method: 'put',
    data: data
  })
}

// 删除品牌目录
export function delBrand(id) {
  return request({
    url: '/config/brand/' + id,
    method: 'delete'
  })
}

import request from '@/utils/request'

// 查询行程管理列表
export function listTrip(query) {
  return request({
    url: '/location/trip/list',
    method: 'get',
    params: query
  })
}

// 查询行程管理详细
export function getTrip(id) {
  return request({
    url: '/location/trip/' + id,
    method: 'get'
  })
}

// 新增行程管理
export function addTrip(data) {
  return request({
    url: '/location/trip',
    method: 'post',
    data: data
  })
}

// 修改行程管理
export function updateTrip(data) {
  return request({
    url: '/location/trip',
    method: 'put',
    data: data
  })
}

// 删除行程管理
export function delTrip(id) {
  return request({
    url: '/location/trip/' + id,
    method: 'delete'
  })
}

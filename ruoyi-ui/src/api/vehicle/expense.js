import request from '@/utils/request'

// 查询消费明细列表
export function listExpense(query) {
  return request({
    url: '/vehicle/expense/list',
    method: 'get',
    params: query
  })
}

// 查询消费明细详细
export function getExpense(expenseId) {
  return request({
    url: '/vehicle/expense/' + expenseId,
    method: 'get'
  })
}

// 新增消费明细
export function addExpense(data) {
  return request({
    url: '/vehicle/expense',
    method: 'post',
    data: data
  })
}

// 修改消费明细
export function updateExpense(data) {
  return request({
    url: '/vehicle/expense',
    method: 'put',
    data: data
  })
}

// 删除消费明细
export function delExpense(expenseId) {
  return request({
    url: '/vehicle/expense/' + expenseId,
    method: 'delete'
  })
}

import request from '@/utils/request'

// 查询我的车列表
export function listCar(query) {
  return request({
    url: '/vehicle/car/list',
    method: 'get',
    params: query
  })
}

// 查询我的车详细
export function getCar(carId) {
  return request({
    url: '/vehicle/car/' + carId,
    method: 'get'
  })
}

// 新增我的车
export function addCar(data) {
  return request({
    url: '/vehicle/car',
    method: 'post',
    data: data
  })
}

// 修改我的车
export function updateCar(data) {
  return request({
    url: '/vehicle/car',
    method: 'put',
    data: data
  })
}

// 删除我的车
export function delCar(carId) {
  return request({
    url: '/vehicle/car/' + carId,
    method: 'delete'
  })
}

// 设置在用车辆
export function setCarInUse(carId) {
  return request({
    url: '/vehicle/car/setInUse/' + carId,
    method: 'put'
  })
}

// 获取当前在用车辆
export function getCurrentInUse() {
  return request({
    url: '/vehicle/car/getCurrentInUse',
    method: 'get'
  })
}

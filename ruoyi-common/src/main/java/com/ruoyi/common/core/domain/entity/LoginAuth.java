package com.ruoyi.common.core.domain.entity;

import java.io.Serializable;
import java.util.Date;

public class <PERSON>ginAuth implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String scene;

    private String openid;

    private Date createTime;

    private Date authTime;

    private Integer status;

    private Integer expire;

    private String token;

    // 构造函数
    public LoginAuth() {
        // 无参构造函数，用于JPA实体管理
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getAuthTime() {
        return authTime;
    }

    public void setAuthTime(Date authTime) {
        this.authTime = authTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getExpire() {
        return expire;
    }

    public void setExpire(Integer expire) {
        this.expire = expire;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    // 其他可能的方法，如验证、转换等
}
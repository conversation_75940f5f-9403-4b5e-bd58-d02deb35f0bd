package com.ruoyi.common.core.domain.model;

/**
 * 用户注册对象
 *
 * <AUTHOR>
 */
public class RegisterBody extends LoginBody {
    //openid
    private String wxCode;
    //app_type
    private String appType;
    //邮箱
    private String email;
    //手机号
    private String phoneNumber;

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    //头像
    private String avatar;


    /**
     * 邮箱
     */
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getWxCode() {
        return wxCode;
    }

    public void setWxCode(String wxCode) {
        this.wxCode = wxCode;
    }

}

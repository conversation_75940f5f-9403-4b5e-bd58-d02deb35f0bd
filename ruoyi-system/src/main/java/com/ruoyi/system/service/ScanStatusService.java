package com.ruoyi.system.service;


import com.ruoyi.common.core.domain.entity.LoginAuth;
import com.ruoyi.common.core.domain.entity.UserAuthInfo;
import com.ruoyi.system.mapper.LoginAuthMapper;
import com.ruoyi.system.mapper.UserAuthInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Optional;

@Service
// 扫描状态服务
public class ScanStatusService {

    // 自动注入LoginAuthRepository实例，用于操作LoginAuth实体类对应的数据库表
    @Autowired
    private LoginAuthMapper loginAuthRepository;

    @Autowired
    private UserAuthInfoMapper userAuthInfoRepository;

    /**
     * 检查扫描状态
     *
     * @param scene 用于标识授权场景的参数
     * @return 返回一个Optional<LoginAuth>对象，如果找到对应的LoginAuth实体则包含该实体，否则为空
     */
    public LoginAuth checkScanStatus(String scene) {
        // 根据场景标识查找对应的LoginAuth实体
        return loginAuthRepository.findByScene(scene);
    }

    /**
     * 检查用户授权状态
     *
     * @param openid
     * @return 返回一个Optional<LoginAuth>对象，如果找到对应的LoginAuth实体则包含该实体，否则为空
     */
    public UserAuthInfo getUserAuthInfo(String openid) {

        // 根据场景标识查找对应的LoginAuth实体
        return userAuthInfoRepository.findByOpenId(openid);
    }

    /**
     * 删除二维码文件
     *
     * @param scene 用于标识二维码文件的参数
     */
    public void deleteQrcode(String scene) {
        // 构造二维码文件的路径
        File file = new File("./qrcode/" + scene + ".png");
        // 如果文件存在
        if (file.exists()) {
            // 删除文件
            file.delete();
        }
    }
}
package com.ruoyi.system.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

@Service
public class AccessTokenService {

    @Value("${wechat.appid}")
    private String appid;

    @Value("${wechat.appsecret}")
    private String appsecret;

    // 定义缓存文件的路径
    private static final String CACHE_FILE = "access_token.ser";

    /**
     * 获取微信小程序的access_token
     *
     * @return 返回有效的access_token，如果缓存有效则直接从缓存中读取，否则重新生成并缓存
     * @throws IOException 如果读取或写入缓存文件时出现IO异常
     * @throws ClassNotFoundException 如果从缓存文件中反序列化对象时发生类未找到异常
     */
    public String getAccessToken() throws IOException, ClassNotFoundException {
        Map<String, Object> tokenData;
        File file = new File(CACHE_FILE);
        if (file.exists()) {
            try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(file))) {
                // 从缓存文件中读取access_token数据
                tokenData = (Map<String, Object>) ois.readObject();
                // 检查缓存的access_token是否已过期
                if ((Long) tokenData.get("expire_time") > System.currentTimeMillis()) {
                    // 如果未过期，则直接返回缓存的access_token
                    return (String) tokenData.get("access_token");
                }
            }
        }
        // 如果缓存不存在或已过期，则重新生成access_token
        return createAccessToken();
    }

    /**
     * 生成微信小程序的access_token
     *
     * @return 返回新生成的access_token
     * @throws IOException 如果与微信服务器通信时出现IO异常
     */
    private String createAccessToken() throws IOException {
        // 创建RestTemplate用于发送HTTP请求
        RestTemplate restTemplate = new RestTemplate();
        // 构造获取access_token的URL
        String url = String.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s", appid, appsecret);
        // 发送GET请求获取access_token
        Map<String, String> response = restTemplate.getForObject(url, Map.class);
        if (response != null && response.containsKey("access_token")) {
            // 创建存储access_token信息的Map
            Map<String, Object> tokenData = new HashMap<>();
            // 将获取的access_token存入Map
            tokenData.put("access_token", response.get("access_token"));
            // 设置access_token的过期时间为当前时间加上7200秒（2小时）
            tokenData.put("expire_time", System.currentTimeMillis() + 7200 * 1000);
            try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(CACHE_FILE))) {
                // 将access_token信息写入缓存文件
                oos.writeObject(tokenData);
            }
            // 返回新生成的access_token
            return response.get("access_token");
        }
        // 如果请求失败或未获取到access_token，则返回null
        return null;
    }
}
package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.entity.LoginAuth;
import com.ruoyi.system.mapper.LoginAuthMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
// 场景服务
public class SceneService {

    @Autowired
    private LoginAuthMapper loginAuthRepository;

    public LoginAuth checkScene(String scene) {
        return loginAuthRepository.findByScene(scene);
    }
}
package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.entity.LoginAuth;
import com.ruoyi.system.mapper.LoginAuthMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
// 取消授权服务
public class CancelAuthService {

    // 自动注入LoginAuthRepository实例，用于操作LoginAuth实体类对应的数据库表
    @Autowired
    private LoginAuthMapper loginAuthRepository;

    /**
     * 取消授权服务
     *
     * @param scene 用于标识授权场景的参数
     * @return 返回一个Optional<LoginAuth>对象，如果找到对应的LoginAuth实体则包含该实体，否则为空
     */
    public LoginAuth cancelAuth(String scene) {
        // 根据场景标识查找对应的LoginAuth实体
        LoginAuth loginAuth = loginAuthRepository.findByScene(scene);

        // 如果找到了对应的LoginAuth实体
        if (loginAuth != null) {
            // 获取LoginAuth实体

            // 设置状态为4，假设4代表“授权已取消”
            loginAuth.setStatus(4);

            // 设置过期时间为2，假设2代表“二维码已过期”
            loginAuth.setExpire(2);

            // 更新数据库中的LoginAuth实体
            loginAuthRepository.updateLoginAuth(loginAuth);

            // 返回包含更新后LoginAuth实体的Optional对象
            return loginAuth;
        }

        // 如果没有找到对应的LoginAuth实体，则返回空的Optional对象
        return loginAuth;
    }
}
package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.LoginAuth;
import com.ruoyi.system.mapper.LoginAuthMapper;
import com.ruoyi.system.service.AccessTokenService;
import com.ruoyi.system.service.QrCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

@Service
public class QrCodeServiceImpl implements QrCodeService {

    @Autowired
    private AccessTokenService accessTokenService;
    @Autowired
    private LoginAuthMapper loginAuthRepository;

    @Override
    public String createQrCode() {
        String accessToken = null;
        try {
            // 调用AccessTokenService的getAccessToken方法获取access_token
            accessToken = accessTokenService.getAccessToken();
        } catch (ClassNotFoundException | IOException e) {
            // 如果在获取access_token时发生ClassNotFoundException异常，则抛出运行时异常
            throw new RuntimeException(e);
        }
        // 如果accessToken为空，说明获取失败，直接返回null
        if (accessToken == null) {
            return null;
        }
        // 创建RestTemplate实例，用于发送HTTP请求
        RestTemplate restTemplate = new RestTemplate();
        // 构造请求URL，将access_token拼接到URL中
        String url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + accessToken;
        // 创建请求参数Map
        Map<String, Object> requestParams = new HashMap<>();
        // 生成随机的scene值，用于标识小程序码
        String scene = String.valueOf(new Random().nextInt(8999999) + 1000000);
        // 设置请求参数
        requestParams.put("page", "pages/index/index"); // 小程序扫码后的跳转页面
        requestParams.put("scene", scene); // scene值
        requestParams.put("check_path", false); // 是否校验path的合法性
        requestParams.put("env_version", "develop"); // 小程序的环境版本，开发时为"develop"

        // 发送POST请求，获取小程序码的二进制数据
        byte[] result = restTemplate.postForObject(url, requestParams, byte[].class);
        // 如果请求结果不为空
        if (result != null) {
            // 构造小程序码文件的保存路径
            // 获取程序的工作目录
            // 使用相对路径构建文件保存目录
            String relativeDirPath = "qrcode/"; // 相对于应用运行目录的路径
            String filename = scene; // 只包含文件名，不包含路径
            File dir = new File(relativeDirPath);
            if (!dir.exists()) {
                dir.mkdirs(); // 如果目录不存在，则创建
            }

            // 现在你可以安全地尝试打开和写入文件
            File file = new File(dir, filename + ".png");
            try (FileOutputStream fos = new FileOutputStream(file)) {
                // 将获取到的小程序码二进制数据写入文件
                fos.write(result);
            }catch (IOException e) {
                throw new RuntimeException(e);
            }

            // 保存到数据库
            // 创建LoginAuth实体对象
            LoginAuth loginAuth = new LoginAuth();
            loginAuth.setScene(scene); // 设置scene值
            loginAuth.setCreateTime(new Date()); // 设置创建时间
            loginAuth.setStatus(1); // 设置状态为初始状态（假设0为初始状态）
            loginAuth.setExpire(7200); // 设置过期时间为7200秒
            // 将LoginAuth实体对象保存到数据库
            loginAuthRepository.insertLoginAuth(loginAuth);

            // 返回生成的小程序码文件名，而不是文件路径
            return filename;
        }
        // 如果请求结果为空，说明创建小程序码失败，返回null
        return null;
    }
}
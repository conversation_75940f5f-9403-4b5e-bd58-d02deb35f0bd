package com.ruoyi.system.service;


import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
// 定义微信服务类
public class WechatService {

    @Value("${wechat.appid}")
    private String appid;

    @Value("${wechat.appsecret}")
    private String appsecret;

    public String getOpenId(String code) {
        RestTemplate restTemplate = new RestTemplate();
        String url = String.format("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code", appid, appsecret, code);
        //打印url
        System.out.println(url);
        // 使用RestTemplate发送GET请求获取响应字符串，并指定响应类型为String
        // 使用String.class作为响应类型
        String responseStr = restTemplate.getForObject(url, String.class);

        try {
            // 创建ObjectMapper对象
            // 手动将响应字符串转换为Map
            ObjectMapper mapper = new ObjectMapper();
            // 使用readValue方法将响应字符串转换为Map类型
            Map<String, String> response = mapper.readValue(responseStr, Map.class);
            // 从Map中获取openid字段的值
            return response.get("openid");
        } catch (Exception e) {
            // 打印异常堆栈信息
            e.printStackTrace();
            // 返回null表示获取openid失败
            return null;
        }
    }
}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.LoginAuthMapper">

    <select id="findByScene" resultType="com.ruoyi.common.core.domain.entity.LoginAuth">
        SELECT * FROM login_auth WHERE scene = #{scene}
    </select>
    <select id="findByOpenid" resultType="com.ruoyi.common.core.domain.entity.LoginAuth">
        SELECT * FROM login_auth WHERE openid = #{openid}
    </select>

    <!-- 插入LoginAuth的SQL语句 -->
    <insert id="insertLoginAuth" parameterType="com.ruoyi.common.core.domain.entity.LoginAuth">
        INSERT INTO login_auth (
            scene,
            create_time,
            status,
            expire
        ) VALUES (
                     #{scene},
                     #{createTime, jdbcType=TIMESTAMP},
                     #{status},
                     #{expire}
                 )
    </insert>


    <update id="updateLoginAuth" parameterType="com.ruoyi.common.core.domain.entity.LoginAuth">
        UPDATE login_auth
        SET openid = #{openid},
        status = #{status},
        auth_time = NOW()
        WHERE id = #{id}
    </update>
</mapper>
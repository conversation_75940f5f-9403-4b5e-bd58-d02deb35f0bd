<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserAuthInfoMapper">
    <resultMap id="UserAuthInfoResultMap" type="com.ruoyi.common.core.domain.entity.UserAuthInfo">
        <id property="id" column="id" />
        <result property="openId" column="open_id" />
        <result property="userId" column="user_id" />
        <result property="appType" column="app_type" />
    </resultMap>

    <select id="findByOpenId" resultMap="UserAuthInfoResultMap">
        SELECT * FROM user_auth_info WHERE open_id = #{openId}
    </select>

    <insert id="insert" parameterType="com.ruoyi.common.core.domain.entity.UserAuthInfo">
        INSERT INTO user_auth_info (
        open_id,
        user_id,
        app_type
        ) VALUES (
        #{openId},
        #{userId},
        #{appType}
        )
    </insert>
</mapper>
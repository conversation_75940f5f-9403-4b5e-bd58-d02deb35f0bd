FROM anapsix/alpine-java:8_server-jre_unlimited
#MAINTAINER

#应用名称，
ARG APPNAME=ruoyi-admin
ARG LOGPATH=/data/logs/${APPNAME}


# JVM优化参数-容器时代的JVM优化解决方案
ENV JAVA_TOOL_OPTIONS="-XX:InitialRAMPercentage=50.0 -XX:MaxRAMPercentage=75.0 -XX:MaxMetaspaceSize=512M -XX:MetaspaceSize=512M -verbose:gc -Xloggc:$LOGPATH/gc.log -XX:ErrorFile=$LOGPATH/hs_err_pid%p.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$LOGPATH/jvm_dump_pid%p.hprof -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+PrintCommandLineFlags"

#配置时区
RUN rm -f /etc/localtime \
    && ln -sv /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && mkdir -p /app  \
    && mkdir -p ${LOGPATH}


WORKDIR /app
VOLUME ["${LOGPATH}"]
ADD ./target/${APPNAME}.jar ./app.jar
#对外暴露的端口
EXPOSE 8989
#-Dspring.profiles.active=${ENV}
#运行时指定环境信息，达到一个镜像在多套环境使用，项目配置文件建议放置到配置中心里
ENTRYPOINT exec java -server  -jar app.jar
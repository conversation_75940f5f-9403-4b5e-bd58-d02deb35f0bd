package com.ruoyi.web.controller.system;


import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.QrCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "系统：扫码登录")
@RestController
@RequestMapping("/api/qrcode")
@Anonymous
public class QrCodeController {

    @Autowired
    private QrCodeService qrCodeService;

    @ApiOperation("生成二维码")
    @GetMapping("/createQrCode")
    public AjaxResult createQrCode() {
        String qrCodeUrl = qrCodeService.createQrCode();

        if (qrCodeUrl != null) {
            AjaxResult ajax = AjaxResult.success();
            ajax.put("scene", qrCodeUrl);
            ajax.put("qrcode", qrCodeUrl + ".png");
            return ajax;
        } else {
            return AjaxResult.error("创建失败");
        }
    }
}
package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.LoginAuth;
import com.ruoyi.common.core.domain.entity.UserAuthInfo;
import com.ruoyi.system.mapper.LoginAuthMapper;
import com.ruoyi.system.mapper.UserAuthInfoMapper;
import com.ruoyi.system.service.WechatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@Api(tags = "系统：扫码登录")
@RestController
@RequestMapping("/api/qrcode")
// OpenId控制器
public class OpenIdController {

    @Autowired
    private WechatService wechatService;

    @Autowired
    private LoginAuthMapper loginAuthMapper;

    @Autowired
    private UserAuthInfoMapper userAuthInfoRepository;

    //第三步  获取openid
    @ApiOperation("获取openid")
    @GetMapping("/getOpenId")
    // 获取openId
    public AjaxResult getOpenId(@RequestParam String scene, @RequestParam String code) {
        // 获取openId
        String openId = wechatService.getOpenId(code);
        if (openId == null) {
            return AjaxResult.error(201, "计算Id失败");
        }

        // 根据scene查询LoginAuth
        LoginAuth loginAuth = loginAuthMapper.findByScene(scene);

        if (loginAuth == null) {
            return AjaxResult.error(201, "授权码不存在");
        }

        if (loginAuth.getExpire() == 2) {
            return AjaxResult.error(201, "授权码已被使用");
        }

        // 判断二维码状态
        if (loginAuth.getStatus() == 3) {
            return AjaxResult.error(201, "授权码已被使用");
        }

        // 更新LoginAuth的openid和status
        loginAuth.setOpenid(openId);
        loginAuth.setStatus(2); // Assuming 2 represents "scanned"
        loginAuthMapper.updateLoginAuth(loginAuth);

        // 判断openid是否绑定用户
        UserAuthInfo byOpenId = userAuthInfoRepository.findByOpenId(openId);
        if (byOpenId != null) {
            // 授权成功
            return AjaxResult.success("授权成功");
        } else {
            // 授权失败
            return (AjaxResult.error(203, "未绑定用户"));
        }

    }

    @ApiOperation("根据code获取openid并查询绑定用户")
    @GetMapping("/queryUserByCode")
    public AjaxResult queryUserByCode(@RequestParam String code) {
        // 获取openId
        String openId = wechatService.getOpenId(code);
        if (openId == null) {
            return AjaxResult.error(201, "计算Id失败");
        }

        // 判断openid是否绑定用户
        UserAuthInfo byOpenId = userAuthInfoRepository.findByOpenId(openId);
        if (byOpenId != null) {
            // 绑定用户存在，返回用户信息（这里只是示例，您可以根据需要返回用户的哪些信息）
            // 注意：为了简化，这里直接返回了UserAuthInfo对象，但在实际中您可能希望返回一个包含更少字段的DTO对象
            return AjaxResult.success(byOpenId);
        } else {
            // 未绑定用户
            return AjaxResult.error(203, "未绑定用户");
        }
    }

    @ApiOperation("根据code获取openid")
    @GetMapping("/getOpenIdByCode")
    public AjaxResult getOpenIdByCode(@RequestParam String code) {
        // 获取openId
        String openId = wechatService.getOpenId(code);
        if (openId == null) {
            return AjaxResult.error(201, "计算Id失败");
        }
        return AjaxResult.success(openId);
    }

}
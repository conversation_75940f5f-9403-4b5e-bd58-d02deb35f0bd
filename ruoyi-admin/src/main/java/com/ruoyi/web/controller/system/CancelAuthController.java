package com.ruoyi.web.controller.system;

// Java Spring Boot equivalent code

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.LoginAuth;
import com.ruoyi.system.service.CancelAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(tags = "系统：取消授权")
@RestController
@RequestMapping("/api/qrcode")
//取消授权控制器
public class CancelAuthController {

    @Autowired // 使用Spring的自动注入功能，注入CancelAuthService的实例
    private CancelAuthService cancelAuthService;

    @ApiOperation("取消授权")
    @GetMapping("/cancelAuth")
    //取消授权

    public AjaxResult cancelAuth(@RequestParam String scene) {
        // 调用CancelAuthService的cancelAuth方法，传入scene参数进行取消授权操作
        // 返回一个Optional<LoginAuth>，可能包含已取消授权的LoginAuth对象，也可能为空
        LoginAuth loginAuth1 = cancelAuthService.cancelAuth(scene);
        if (loginAuth1 != null) {
            // 如果Optional包含值（即取消授权成功）
            return AjaxResult.success("已取消授权");
        } else {
            // 如果Optional为空（即取消授权失败，可能由于scene不存在）
            return AjaxResult.error(202, "取消失败，scene不存在");
        }

    }
}
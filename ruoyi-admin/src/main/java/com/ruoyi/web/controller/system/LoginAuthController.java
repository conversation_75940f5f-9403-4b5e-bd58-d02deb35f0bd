package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.LoginAuth;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.mapper.LoginAuthMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Map;
import java.util.Optional;

@RestController
// 登录授权控制器
public class LoginAuthController {

    @Autowired
    private LoginAuthMapper loginAuthMapper;

    // 第四步 登录授权
    @GetMapping("/loginAuth")
    // 登录授权
    public AjaxResult loginAuth(@RequestParam String scene) {

        // 根据场景查找登录授权信息
        LoginAuth loginAuth = loginAuthMapper.findByScene(scene);

        // 如果登录授权信息存在
        if (loginAuth != null) {

            // 设置状态为已授权（假设3表示已授权）
            loginAuth.setStatus(3); // Assuming 3 represents "authorized"
            // 设置令牌过期状态（假设2表示二维码已过期）
            loginAuth.setExpire(2); // Assuming 2 represents "QR code expired"
            // 保存登录授权信息
            loginAuthMapper.updateLoginAuth(loginAuth);
            return AjaxResult.success("已授权");
        } else {
            // 返回授权失败的响应
            return AjaxResult.error("授权失败，scene不存在");
        }
    }
}
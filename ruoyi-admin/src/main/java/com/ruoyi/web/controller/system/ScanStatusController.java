package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.domain.entity.LoginAuth;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.system.service.ScanStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Api(tags = "系统：扫码登录")
@RequestMapping("/api/qrcode")
@RestController
public class ScanStatusController {

    @Autowired
    private ScanStatusService scanStatusService;
    @Autowired
    private SysLoginService loginService;

    // 检查二维码状态
    @ApiOperation("查询二维码状态")
    @GetMapping("/checkScanStatus")
    //检查扫码状态
    public ResponseEntity<?> checkScanStatus(@RequestParam String scene) {
        LoginAuth loginAuth = scanStatusService.checkScanStatus(scene);
        // 创建返回结果的Map对象
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        // 获取登录授权的状态
        int status = loginAuth.getStatus();
        // 根据状态判断登录授权的情况
        switch (status) {
            case 1:
                // 状态为1时，表示需要使用微信扫码登录
                result.put("msg", "请使用微信扫码");
                result.put("status", status);
                break;
            case 2:
                // 状态为2时，表示已经扫码，需要点击授权登录
                result.put("msg", "已扫码，请点击授权登录");
                result.put("status", status);
                break;
            case 3:
                // 状态为3时，表示登录成功
                if (loginAuth.getOpenid() != null) {
                    // 假设这个方法是删除二维码的，与PHP中的操作类似
                    scanStatusService.deleteQrcode(scene); // Assuming this deletes the QR code as in PHP
                    String token = loginService.openIdLogin(loginAuth.getOpenid());
                    result.put("msg", "登录成功");
                    result.put("status", status);
                    result.put("token", token);
                }
                break;
            case 4:
                // 假设这个方法是删除二维码的，与PHP中的操作类似
                scanStatusService.deleteQrcode(scene); // Assuming this deletes the QR code as in PHP
                // 状态为4时，表示已取消授权
                result.put("msg", "已取消授权");
                result.put("status", status);
                break;
            default:
                // 如果状态不是以上任何一种情况，则返回错误请求
                result.put("code", 204);
                result.put("msg", "该二维码无法登录");
                return ResponseEntity.badRequest().body(result);
        }
        // 返回成功的响应结果
        return ResponseEntity.ok(result);
    }


}
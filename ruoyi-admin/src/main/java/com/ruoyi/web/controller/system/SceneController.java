package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.LoginAuth;
import com.ruoyi.system.service.SceneService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "系统：扫码登录")
@RestController
@RequestMapping("/api/qrcode")
public class SceneController {

    // 使用@Autowired注解自动注入SceneService实例
    @Autowired
    private SceneService sceneService;

    // 使用@GetMapping注解映射HTTP GET请求到checkScene方法  小程序扫码
    @ApiOperation("查询二维码状态")
    @GetMapping("/checkScene")
    //检查场景码
    public AjaxResult checkScene(@RequestParam String scene) {
        // 调用SceneService的checkScene方法，并传入scene参数
        // 该方法返回一个Optional<LoginAuth>对象，可能包含LoginAuth实例，也可能为空
        LoginAuth loginAuth1 = sceneService.checkScene(scene);
        if (loginAuth1 != null) {
            return AjaxResult.success("获取成功");
        } else {
            return AjaxResult.error(204, "参数错误");
        }
    }
}
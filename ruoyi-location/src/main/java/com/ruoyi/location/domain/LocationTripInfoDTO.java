package com.ruoyi.location.domain;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 行程管理详细信息DTO
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
public class LocationTripInfoDTO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 行程信息 */
    private LocationTrip trip;

    /** 车牌号 */
    private String licensePlate;

    /** 行驶总里程 */
    private Long totalMileage;

    /** 品牌名称 */
    private String brand;

    /** 型号名称 */
    private String model;

    public LocationTrip getTrip() {
        return trip;
    }

    public void setTrip(LocationTrip trip) {
        this.trip = trip;
    }

    public String getLicensePlate() {
        return licensePlate;
    }

    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate;
    }

    public Long getTotalMileage() {
        return totalMileage;
    }

    public void setTotalMileage(Long totalMileage) {
        this.totalMileage = totalMileage;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }
} 
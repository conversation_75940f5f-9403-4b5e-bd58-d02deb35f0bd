package com.ruoyi.location.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 行程轨迹点对象 location_trip_detail
 * 
 * <AUTHOR>
 * @date 2024-12-17
 */
public class LocationTripDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 轨迹点ID */
    private Long id;

    /** 行程ID */
    @Excel(name = "行程ID")
    private Long tripId;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 时间戳 */
    @Excel(name = "时间戳")
    private Long timestamp;

    /** 精度(米) */
    @Excel(name = "精度(米)")
    private BigDecimal accuracy;

    /** 海拔(米) */
    @Excel(name = "海拔(米)")
    private BigDecimal altitude;

    /** 速度(米/秒) */
    @Excel(name = "速度(米/秒)")
    private BigDecimal speed;

    /** 方向(度) */
    @Excel(name = "方向(度)")
    private BigDecimal bearing;

    /** 卫星数 */
    @Excel(name = "卫星数")
    private Long satellites;

    /** 定位类型 */
    @Excel(name = "定位类型")
    private String provider;

    /** 定位提供者 */
    @Excel(name = "定位提供者")
    private Long locationProvider;

    /** 是否已上传 */
    @Excel(name = "是否已上传")
    private Integer isUploaded;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTripId(Long tripId) 
    {
        this.tripId = tripId;
    }

    public Long getTripId() 
    {
        return tripId;
    }
    public void setLatitude(BigDecimal latitude) 
    {
        this.latitude = latitude;
    }

    public BigDecimal getLatitude() 
    {
        return latitude;
    }
    public void setLongitude(BigDecimal longitude) 
    {
        this.longitude = longitude;
    }

    public BigDecimal getLongitude() 
    {
        return longitude;
    }
    public void setTimestamp(Long timestamp) 
    {
        this.timestamp = timestamp;
    }

    public Long getTimestamp() 
    {
        return timestamp;
    }
    public void setAccuracy(BigDecimal accuracy) 
    {
        this.accuracy = accuracy;
    }

    public BigDecimal getAccuracy() 
    {
        return accuracy;
    }
    public void setAltitude(BigDecimal altitude) 
    {
        this.altitude = altitude;
    }

    public BigDecimal getAltitude() 
    {
        return altitude;
    }
    public void setSpeed(BigDecimal speed) 
    {
        this.speed = speed;
    }

    public BigDecimal getSpeed() 
    {
        return speed;
    }
    public void setBearing(BigDecimal bearing) 
    {
        this.bearing = bearing;
    }

    public BigDecimal getBearing() 
    {
        return bearing;
    }
    public void setSatellites(Long satellites) 
    {
        this.satellites = satellites;
    }

    public Long getSatellites() 
    {
        return satellites;
    }
    public void setProvider(String provider) 
    {
        this.provider = provider;
    }

    public String getProvider() 
    {
        return provider;
    }
    public void setLocationProvider(Long locationProvider) 
    {
        this.locationProvider = locationProvider;
    }

    public Long getLocationProvider() 
    {
        return locationProvider;
    }
    public void setIsUploaded(Integer isUploaded) 
    {
        this.isUploaded = isUploaded;
    }

    public Integer getIsUploaded() 
    {
        return isUploaded;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("tripId", getTripId())
            .append("latitude", getLatitude())
            .append("longitude", getLongitude())
            .append("timestamp", getTimestamp())
            .append("accuracy", getAccuracy())
            .append("altitude", getAltitude())
            .append("speed", getSpeed())
            .append("bearing", getBearing())
            .append("satellites", getSatellites())
            .append("provider", getProvider())
            .append("locationProvider", getLocationProvider())
            .append("isUploaded", getIsUploaded())
            .append("createTime", getCreateTime())
            .toString();
    }
}

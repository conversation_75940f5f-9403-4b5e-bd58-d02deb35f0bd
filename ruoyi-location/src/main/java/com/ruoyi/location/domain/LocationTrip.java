package com.ruoyi.location.domain;

import java.math.BigDecimal;
import java.util.List;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Calendar;
import java.text.ParseException;

/**
 * 行程管理对象 location_trip
 * 
 * <AUTHOR>
 * @date 2024-12-17
 */
public class LocationTrip extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 行程ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;

    /** 车辆ID */
    @Excel(name = "车辆ID")
    private Long carId;

    /** 用户名称 */
    private String userName;

    /** 本地 ID */
    private Long localId;

    /** 行程类型 */
    private Long type;

    /** 开始时间 */
    @Excel(name = "开始时间")
    private Long startTime;

    /** 结束时间 */
    @Excel(name = "结束时间")
    private Long endTime;

    /** 总距离(米) */
    @Excel(name = "总距离(米)")
    private BigDecimal totalDistance;

    /** 总时长(秒) */
    @Excel(name = "总时长(秒)")
    private Long totalTime;

    /** 运动时长(秒) */
    @Excel(name = "运动时长(秒)")
    private Long activeTime;

    /** 最高海拔(米) */
    private BigDecimal maxAltitude;

    /** 最低海拔(米) */
    private BigDecimal minAltitude;

    /** 平均海拔(米) */
    private BigDecimal avgAltitude;

    /** 最大速度(米/秒) */
    private BigDecimal maxSpeed;

    /** 平均速度(米/秒) */
    private BigDecimal avgSpeed;

    /** 最大加速度(米/秒²) */
    private BigDecimal maxAcceleration;

    /** 起点纬度 */
    private BigDecimal startLatitude;

    /** 起点经度 */
    private BigDecimal startLongitude;

    /** 终点纬度 */
    private BigDecimal endLatitude;

    /** 终点经度 */
    private BigDecimal endLongitude;

    /** 行程轨迹点信息 */
    private List<LocationTripDetail> locationTripDetailList;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setUserId(String userId) 
    {
        this.userId = userId;
    }

    public String getUserId() 
    {
        return userId;
    }
    public void setCarId(Long carId) {
        this.carId = carId;
    }

    public Long getCarId() {
        return carId;
    }
    public void setLocalId(Long localId)
    {
        this.localId = localId;
    }

    public Long getLocalId()
    {
        return localId;
    }
    public void setType(Long type) 
    {
        this.type = type;
    }

    public Long getType() 
    {
        return type;
    }
    public void setStartTime(Object startTime) {
        if (startTime instanceof String && ((String) startTime).contains("-")) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date date = sdf.parse((String) startTime);
                this.startTime = date.getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
        } else if (startTime instanceof Long) {
            this.startTime = (Long) startTime;
        } else if (startTime != null) {
            this.startTime = Long.parseLong(startTime.toString());
        }
    }

    public Long getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Object endTime) {
        if (endTime instanceof String && ((String) endTime).contains("-")) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date date = sdf.parse((String) endTime);
                // 设置为当天最后一毫秒
                Calendar cal = Calendar.getInstance();
                cal.setTime(date);
                cal.set(Calendar.HOUR_OF_DAY, 23);
                cal.set(Calendar.MINUTE, 59);
                cal.set(Calendar.SECOND, 59);
                cal.set(Calendar.MILLISECOND, 999);
                this.endTime = cal.getTimeInMillis();
            } catch (ParseException e) {
                e.printStackTrace();
            }
        } else if (endTime instanceof Long) {
            this.endTime = (Long) endTime;
        } else if (endTime != null) {
            this.endTime = Long.parseLong(endTime.toString());
        }
    }

    public Long getEndTime() 
    {
        return endTime;
    }
    public void setTotalDistance(BigDecimal totalDistance) 
    {
        this.totalDistance = totalDistance;
    }

    public BigDecimal getTotalDistance() 
    {
        return totalDistance;
    }
    public void setTotalTime(Long totalTime) 
    {
        this.totalTime = totalTime;
    }

    public Long getTotalTime() 
    {
        return totalTime;
    }
    public void setActiveTime(Long activeTime) 
    {
        this.activeTime = activeTime;
    }

    public Long getActiveTime() 
    {
        return activeTime;
    }
    public void setMaxAltitude(BigDecimal maxAltitude) 
    {
        this.maxAltitude = maxAltitude;
    }

    public BigDecimal getMaxAltitude() 
    {
        return maxAltitude;
    }
    public void setMinAltitude(BigDecimal minAltitude) 
    {
        this.minAltitude = minAltitude;
    }

    public BigDecimal getMinAltitude() 
    {
        return minAltitude;
    }
    public void setAvgAltitude(BigDecimal avgAltitude) 
    {
        this.avgAltitude = avgAltitude;
    }

    public BigDecimal getAvgAltitude() 
    {
        return avgAltitude;
    }
    public void setMaxSpeed(BigDecimal maxSpeed) 
    {
        this.maxSpeed = maxSpeed;
    }

    public BigDecimal getMaxSpeed() 
    {
        return maxSpeed;
    }
    public void setAvgSpeed(BigDecimal avgSpeed) 
    {
        this.avgSpeed = avgSpeed;
    }

    public BigDecimal getAvgSpeed() 
    {
        return avgSpeed;
    }
    public void setMaxAcceleration(BigDecimal maxAcceleration) 
    {
        this.maxAcceleration = maxAcceleration;
    }

    public BigDecimal getMaxAcceleration() 
    {
        return maxAcceleration;
    }
    public void setStartLatitude(BigDecimal startLatitude) 
    {
        this.startLatitude = startLatitude;
    }

    public BigDecimal getStartLatitude() 
    {
        return startLatitude;
    }
    public void setStartLongitude(BigDecimal startLongitude) 
    {
        this.startLongitude = startLongitude;
    }

    public BigDecimal getStartLongitude() 
    {
        return startLongitude;
    }
    public void setEndLatitude(BigDecimal endLatitude) 
    {
        this.endLatitude = endLatitude;
    }

    public BigDecimal getEndLatitude() 
    {
        return endLatitude;
    }
    public void setEndLongitude(BigDecimal endLongitude) 
    {
        this.endLongitude = endLongitude;
    }

    public BigDecimal getEndLongitude() 
    {
        return endLongitude;
    }

    public List<LocationTripDetail> getLocationTripDetailList()
    {
        return locationTripDetailList;
    }

    public void setLocationTripDetailList(List<LocationTripDetail> locationTripDetailList)
    {
        this.locationTripDetailList = locationTripDetailList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("carId", getCarId())
            .append("localId", getLocalId())
            .append("type", getType())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("totalDistance", getTotalDistance())
            .append("totalTime", getTotalTime())
            .append("activeTime", getActiveTime())
            .append("maxAltitude", getMaxAltitude())
            .append("minAltitude", getMinAltitude())
            .append("avgAltitude", getAvgAltitude())
            .append("maxSpeed", getMaxSpeed())
            .append("avgSpeed", getAvgSpeed())
            .append("maxAcceleration", getMaxAcceleration())
            .append("startLatitude", getStartLatitude())
            .append("startLongitude", getStartLongitude())
            .append("endLatitude", getEndLatitude())
            .append("endLongitude", getEndLongitude())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("locationTripDetailList", getLocationTripDetailList())
            .toString();
    }
}

package com.ruoyi.location.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.location.domain.LocationTrip;
import com.ruoyi.location.domain.LocationTripDetail;
import org.apache.ibatis.annotations.Param;

/**
 * 行程管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
public interface LocationTripMapper
{
    /**
     * 查询行程管理
     *
     * @param id 行程管理主键
     * @return 行程管理
     */
    public LocationTrip selectLocationTripById(Long id);

    /**
     * 查询行程管理列表
     *
     * @param locationTrip 行程管理
     * @return 行程管理集合
     */
    public List<LocationTrip> selectLocationTripList(LocationTrip locationTrip);

    /**
     * 新增行程管理
     *
     * @param locationTrip 行程管理
     * @return 结果
     */
    public int insertLocationTrip(LocationTrip locationTrip);

    /**
     * 修改行程管理
     *
     * @param locationTrip 行程管理
     * @return 结果
     */
    public int updateLocationTrip(LocationTrip locationTrip);

    /**
     * 删除行程管理
     *
     * @param id 行程管理主键
     * @return 结果
     */
    public int deleteLocationTripById(Long id);

    /**
     * 批量删除行程管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLocationTripByIds(Long[] ids);

    /**
     * 批量删除行程轨迹点
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLocationTripDetailByTripIds(Long[] ids);

    /**
     * 批量新增行程轨迹点
     *
     * @param locationTripDetailList 行程轨迹点列表
     * @return 结果
     */
    public int batchLocationTripDetail(List<LocationTripDetail> locationTripDetailList);


    /**
     * 通过行程管理主键删除行程轨迹点信息
     *
     * @param id 行程管理ID
     * @return 结果
     */
    public int deleteLocationTripDetailByTripId(Long id);

    /**
     * 获取用户最新的轨迹点信息
     *
     * @param userId 用户ID
     * @return 最新的轨迹点信息
     */
    public LocationTripDetail selectLatestLocationByUserId(String userId);

    /**
     * 查询当天行程统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectTodayTripStats(String userId);

    /**
     * 获取用户指定车辆的最新轨迹点信息
     *
     * @param userId 用户ID
     * @param carId 车辆ID
     * @return 最新的轨迹点信息
     */
    public LocationTripDetail selectLatestLocationByUserIdAndCarId(@Param("userId") String userId, @Param("carId") Long carId);

    /**
     * 根据本地ID和用户ID查询行程
     *
     * @param localId 本地ID
     * @param userId 用户ID
     * @return 行程信息
     */
    public LocationTrip selectLocationTripByLocalIdAndUserId(@Param("localId") Long localId, @Param("userId") String userId);
}

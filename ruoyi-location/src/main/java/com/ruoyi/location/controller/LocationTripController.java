package com.ruoyi.location.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.location.domain.LocationTripDetail;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.location.domain.LocationTrip;
import com.ruoyi.location.service.ILocationTripService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 行程管理Controller
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@RestController
@RequestMapping("/location/trip")
public class LocationTripController extends BaseController {
    @Autowired
    private ILocationTripService locationTripService;

    /**
     * 查询行程管理列表
     */
    @PreAuthorize("@ss.hasPermi('location:trip:list')")
    @GetMapping("/list")
    @DataScope(userAlias = "u")
    public TableDataInfo list(LocationTrip locationTrip) {
        startPage();
        List<LocationTrip> list = locationTripService.selectLocationTripList(locationTrip);
        return getDataTable(list);
    }

    /**
     * 导出行程管理列表
     */
    @PreAuthorize("@ss.hasPermi('location:trip:export')")
    @Log(title = "行程管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LocationTrip locationTrip) {
        List<LocationTrip> list = locationTripService.selectLocationTripList(locationTrip);
        ExcelUtil<LocationTrip> util = new ExcelUtil<LocationTrip>(LocationTrip.class);
        util.exportExcel(response, list, "行程管理数据");
    }

    /**
     * 获取行程管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('location:trip:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(locationTripService.selectLocationTripInfoById(id));
    }

    /**
     * 新增行程管理
     */
    @PreAuthorize("@ss.hasPermi('location:trip:add')")
    @Log(title = "行程管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LocationTrip locationTrip) {
        locationTrip.setCreateBy(getUsername());
        locationTrip.setLocalId(locationTrip.getId());
        locationTrip.setId(null);

        // 检查是否存在相同LocalId的行程
        LocationTrip existingTrip = locationTripService.selectLocationTripByLocalId(locationTrip.getLocalId(), SecurityUtils.getUserId().toString());
        
        Map<String, Object> data = new HashMap<>(2);
        if (existingTrip != null) {
            // 如果存在相同LocalId的行程，执行更新操作
            locationTrip.setId(existingTrip.getId());
            locationTrip.setUpdateBy(getUsername());
            locationTripService.updateLocationTrip(locationTrip);
            data.put("tripId", existingTrip.getId());
            data.put("localId", locationTrip.getLocalId());
            return AjaxResult.success("行程已更新", data);
        }

        // 不存在相同LocalId的行程，执行新增操作
        locationTripService.insertLocationTrip(locationTrip);
        data.put("tripId", locationTrip.getId());
        data.put("localId", locationTrip.getLocalId());
        return AjaxResult.success(data);
    }

    /**
     * 修改行程管理
     */
    @PreAuthorize("@ss.hasPermi('location:trip:edit')")
    @Log(title = "行程管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LocationTrip locationTrip) {
        locationTrip.setUpdateBy(getUsername());
        return toAjax(locationTripService.updateLocationTrip(locationTrip));
    }

    /**
     * 删除行程管理
     */
    @PreAuthorize("@ss.hasPermi('location:trip:remove')")
    @Log(title = "行程管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(locationTripService.deleteLocationTripByIds(ids));
    }

    /**
     * 批量添加行程轨迹点
     */
    @PreAuthorize("@ss.hasPermi('location:trip:add')")
    @Log(title = "行程管理", businessType = BusinessType.INSERT)
    @PostMapping("/details/batch")
    public AjaxResult addTripDetails(
            @RequestParam("tripId") Long tripId,
            @RequestBody Map<String, List<LocationTripDetail>> requestBody
    ) {
        try {
            List<LocationTripDetail> details = requestBody.get("details");
            if (details == null) {
                return AjaxResult.error("请求体格式错误：缺少 details 字段");
            }

            // 为所有轨迹点设置行程ID
            details.forEach(detail -> detail.setTripId(tripId));

            // 调用service层处理批量插入
            return toAjax(locationTripService.batchInsertTripDetails(details));
        } catch (Exception e) {
            logger.error("批量添加行程轨迹点失败", e);
            return AjaxResult.error("批量添加行程轨迹点失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户最新的轨迹点信息
     */
    @PreAuthorize("@ss.hasPermi('location:trip:query')")
    @GetMapping("/latest-location/{userId}")
    public AjaxResult getLatestLocation(@PathVariable("userId") String userId, @RequestParam(required = false) Long carId) {
        try {
            Map<String, Object> result = locationTripService.selectLatestLocationAndMileage(userId, carId);
            if (result != null && !result.isEmpty()) {
                return AjaxResult.success(result);
            } else {
                return AjaxResult.error("未找到该用户的轨迹点信息");
            }
        } catch (Exception e) {
            logger.error("获取最新轨迹点失败", e);
            return AjaxResult.error("获取最新轨迹点失败：" + e.getMessage());
        }
    }

    /**
     * 获取当天行程统计信息
     */
    @PreAuthorize("@ss.hasPermi('location:trip:query')")
    @GetMapping("/today-stats")
    public AjaxResult getTodayStats() {
        try {
            String userId = SecurityUtils.getUserId().toString();
            Map<String, Object> stats = locationTripService.getTodayTripStats(userId);
            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("获取当天行程统计信息失败", e);
            return AjaxResult.error("获取当天行程统计信息失败：" + e.getMessage());
        }
    }
}

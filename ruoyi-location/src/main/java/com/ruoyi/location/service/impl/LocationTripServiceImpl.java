package com.ruoyi.location.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.location.domain.LocationTripDetail;
import com.ruoyi.location.mapper.LocationTripMapper;
import com.ruoyi.location.domain.LocationTrip;
import com.ruoyi.location.domain.LocationTripInfoDTO;
import com.ruoyi.location.service.ILocationTripService;
import com.ruoyi.vehicle.service.ICarService;
import com.ruoyi.vehicle.domain.Car;
import org.springframework.util.CollectionUtils;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Calendar;
import java.text.ParseException;
import com.ruoyi.common.utils.SecurityUtils;
import java.util.Map;
import java.util.HashMap;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 行程管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@Service
public class LocationTripServiceImpl implements ILocationTripService
{
    @Autowired
    private LocationTripMapper locationTripMapper;

    @Autowired
    private ICarService carService;

    /**
     * 查询行程管理
     *
     * @param id 行程管理主键
     * @return 行程管理
     */
    @Override
    public LocationTrip selectLocationTripById(Long id)
    {
        return locationTripMapper.selectLocationTripById(id);
    }

    /**
     * 查询行程管理列表
     *
     * @param locationTrip 行程管理
     * @return 行程管理
     */
    @Override
    public List<LocationTrip> selectLocationTripList(LocationTrip locationTrip)
    {
        // 如果不是管理员,只能查看自己的行程
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId()))
        {
            locationTrip.setUserId(SecurityUtils.getUserId().toString());
        }
        try {
            // 如果startTime是日期字符串格式,则转换为时间戳
            if (locationTrip.getStartTime() != null && locationTrip.getStartTime().toString().contains("-")) {
                String startTimeStr = locationTrip.getStartTime().toString();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date startDate = sdf.parse(startTimeStr);
                locationTrip.setStartTime(startDate.getTime());
            }

            // 如果endTime是日期字符串格式,则转换为时间戳
            if (locationTrip.getEndTime() != null && locationTrip.getEndTime().toString().contains("-")) {
                String endTimeStr = locationTrip.getEndTime().toString();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date endDate = sdf.parse(endTimeStr);
                // 设置为当天最后一毫秒
                Calendar cal = Calendar.getInstance();
                cal.setTime(endDate);
                cal.set(Calendar.HOUR_OF_DAY, 23);
                cal.set(Calendar.MINUTE, 59);
                cal.set(Calendar.SECOND, 59);
                cal.set(Calendar.MILLISECOND, 999);
                locationTrip.setEndTime(cal.getTimeInMillis());
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return locationTripMapper.selectLocationTripList(locationTrip);
    }

    /**
     * 新增行程管理
     *
     * @param locationTrip 行程管理
     * @return 结果
     */
    @Transactional
    @Override
    public int insertLocationTrip(LocationTrip locationTrip)
    {
        locationTrip.setUserId(SecurityUtils.getUserId().toString());
        locationTrip.setCreateTime(DateUtils.getNowDate());
        int rows = locationTripMapper.insertLocationTrip(locationTrip);
        insertLocationTripDetail(locationTrip);
        
        // 更新车辆总里程和驾驶时间
        if (locationTrip.getCarId() != null) {
            Car car = carService.selectCarByCarId(locationTrip.getCarId());
            if (car != null) {
                // 更新总里程（直接累加米数）
                if (locationTrip.getTotalDistance() != null) {
                    Long currentMileage = car.getTotalMileage() != null ? car.getTotalMileage() : 0L;
                    car.setTotalMileage(currentMileage + locationTrip.getTotalDistance().longValue());
                }
                
                // 更新总驾驶时间（直接使用毫秒）
                if (locationTrip.getTotalTime() != null) {
                    Long currentTotalDrivingTime = car.getTotalDrivingTime();
                    if (currentTotalDrivingTime == null) {
                        currentTotalDrivingTime = 0L;
                    }
                    car.setTotalDrivingTime(currentTotalDrivingTime + locationTrip.getTotalTime());
                }
                
                carService.updateCar(car);
            }
        }
        return rows;
    }

    /**
     * 修改行程管理
     *
     * @param locationTrip 行程管理
     * @return 结果
     */
    @Transactional
    @Override
    public int updateLocationTrip(LocationTrip locationTrip)
    {
        locationTrip.setUpdateTime(DateUtils.getNowDate());
        
        // 获取原行程信息
        LocationTrip oldTrip = locationTripMapper.selectLocationTripById(locationTrip.getId());
        if (oldTrip != null && oldTrip.getCarId() != null && oldTrip.getCarId().equals(locationTrip.getCarId())) {
            Car car = carService.selectCarByCarId(locationTrip.getCarId());
            if (car != null) {
                // 更新总里程（计算米数差值）
                if (oldTrip.getTotalDistance() != null && locationTrip.getTotalDistance() != null) {
                    BigDecimal distanceDiff = locationTrip.getTotalDistance().subtract(oldTrip.getTotalDistance());
                    Long currentMileage = car.getTotalMileage() != null ? car.getTotalMileage() : 0L;
                    car.setTotalMileage(currentMileage + distanceDiff.longValue());
                }
                
                // 更新总驾驶时间（计算毫秒差值）
                if (oldTrip.getTotalTime() != null && locationTrip.getTotalTime() != null) {
                    Long currentTotalDrivingTime = car.getTotalDrivingTime();
                    if (currentTotalDrivingTime == null) {
                        currentTotalDrivingTime = 0L;
                    }
                    Long timeDiff = locationTrip.getTotalTime() - oldTrip.getTotalTime();
                    car.setTotalDrivingTime(currentTotalDrivingTime + timeDiff);
                }
                
                carService.updateCar(car);
            }
        }
        
        return locationTripMapper.updateLocationTrip(locationTrip);
    }

    /**
     * 批量删除行程管理
     *
     * @param ids 需要删除的行程管理主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteLocationTripByIds(Long[] ids)
    {
        locationTripMapper.deleteLocationTripDetailByTripIds(ids);
        return locationTripMapper.deleteLocationTripByIds(ids);
    }

    /**
     * 删除行程管理信息
     *
     * @param id 行程管理主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteLocationTripById(Long id)
    {
        // 获取要删除的行程信息
        LocationTrip trip = locationTripMapper.selectLocationTripById(id);
        if (trip != null && trip.getCarId() != null) {
            Car car = carService.selectCarByCarId(trip.getCarId());
            if (car != null) {
                // 更新总里程（减去米数）
                if (trip.getTotalDistance() != null) {
                    Long currentMileage = car.getTotalMileage() != null ? car.getTotalMileage() : 0L;
                    car.setTotalMileage(currentMileage - trip.getTotalDistance().longValue());
                }
                
                // 更新总驾驶时间（减去毫秒数）
                if (trip.getTotalTime() != null) {
                    Long currentTotalDrivingTime = car.getTotalDrivingTime();
                    if (currentTotalDrivingTime == null) {
                        currentTotalDrivingTime = 0L;
                    }
                    car.setTotalDrivingTime(currentTotalDrivingTime - trip.getTotalTime());
                }
                
                carService.updateCar(car);
            }
        }
        
        locationTripMapper.deleteLocationTripDetailByTripId(id);
        return locationTripMapper.deleteLocationTripById(id);
    }

    /**
     * 新增行程轨迹点信息
     *
     * @param locationTrip 行程管理对象
     */
    public void insertLocationTripDetail(LocationTrip locationTrip)
    {
        List<LocationTripDetail> locationTripDetailList = locationTrip.getLocationTripDetailList();
        Long id = locationTrip.getId();
        if (StringUtils.isNotNull(locationTripDetailList))
        {
            List<LocationTripDetail> list = new ArrayList<LocationTripDetail>();
            for (LocationTripDetail locationTripDetail : locationTripDetailList)
            {
                locationTripDetail.setTripId(id);
                list.add(locationTripDetail);
            }
            if (list.size() > 0)
            {
                locationTripMapper.batchLocationTripDetail(list);
            }
        }
    }

    /**
     * 批量插入行程轨迹点
     *
     * @param details 轨迹点列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchInsertTripDetails(List<LocationTripDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return 0;
        }
        // 设置创建时间
        for (LocationTripDetail detail : details) {
            if (detail.getCreateTime() == null) {
                detail.setCreateTime(DateUtils.getNowDate());
            }
        }
        return locationTripMapper.batchLocationTripDetail(details);
    }

    /**
     * 获取用户最新的轨迹点信息
     *
     * @param userId 用户ID
     * @return 最新的轨迹点信息
     */
    @Override
    public LocationTripDetail selectLatestLocationByUserId(String userId) {
        return locationTripMapper.selectLatestLocationByUserId(userId);
    }

    /**
     * 获取当天行程统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getTodayTripStats(String userId) {
        Map<String, Object> stats = locationTripMapper.selectTodayTripStats(userId);
        if (stats == null) {
            stats = new HashMap<>();
            stats.put("totalDistance", 0.0);
            stats.put("totalTime", 0L);
            stats.put("avgSpeed", 0.0);
            stats.put("maxSpeed", 0.0);
        }
        return stats;
    }

    /**
     * 查询行程管理详细信息（包含车辆信息）
     *
     * @param id 行程管理主键
     * @return 行程管理详细信息
     */
    @Override
    public LocationTripInfoDTO selectLocationTripInfoById(Long id) {
        LocationTrip trip = locationTripMapper.selectLocationTripById(id);
        if (trip == null) {
            return null;
        }

        LocationTripInfoDTO tripInfo = new LocationTripInfoDTO();
        tripInfo.setTrip(trip);

        // 获取车辆信息
        if (trip.getCarId() != null) {
            Car car = carService.selectCarByCarId(trip.getCarId());
            if (car != null) {
                tripInfo.setLicensePlate(car.getLicensePlate());
                // 将里程从米转换为千米
                tripInfo.setTotalMileage(car.getTotalMileage() != null ? car.getTotalMileage() / 1000 : null);
                tripInfo.setBrand(car.getBrand());
                tripInfo.setModel(car.getModel());
            }
        }

        return tripInfo;
    }

    @Override
    public Map<String, Object> selectLatestLocationAndMileage(String userId, Long carId) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取最新轨迹点
        LocationTripDetail latestLocation = locationTripMapper.selectLatestLocationByUserIdAndCarId(userId, carId);
        if (latestLocation != null) {
            result.put("latestLocation", latestLocation);
        }
        
        // 如果指定了车辆ID，获取车辆总里程
        if (carId != null) {
            Car car = carService.selectCarByCarId(carId);
            if (car != null) {
                // 将里程从米转换为千米
                result.put("totalMileage", car.getTotalMileage() != null ? car.getTotalMileage() : null);
                // 添加上次校准时间
                result.put("lastMileageCalibration", car.getLastMileageCalibration());
            }
        }
        
        return result;
    }

    /**
     * 根据本地ID和用户ID查询行程
     *
     * @param localId 本地ID
     * @param userId 用户ID
     * @return 行程信息
     */
    @Override
    public LocationTrip selectLocationTripByLocalId(Long localId, String userId) {
        return locationTripMapper.selectLocationTripByLocalIdAndUserId(localId, userId);
    }
}

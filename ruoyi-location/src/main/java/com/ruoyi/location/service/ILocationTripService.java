package com.ruoyi.location.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.location.domain.LocationTrip;
import com.ruoyi.location.domain.LocationTripDetail;
import com.ruoyi.location.domain.LocationTripInfoDTO;

/**
 * 行程管理Service接口
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
public interface ILocationTripService
{
    /**
     * 查询行程管理
     *
     * @param id 行程管理主键
     * @return 行程管理
     */
    public LocationTrip selectLocationTripById(Long id);

    /**
     * 查询行程管理列表
     *
     * @param locationTrip 行程管理
     * @return 行程管理集合
     */
    public List<LocationTrip> selectLocationTripList(LocationTrip locationTrip);

    /**
     * 新增行程管理
     *
     * @param locationTrip 行程管理
     * @return 结果
     */
    public int insertLocationTrip(LocationTrip locationTrip);

    /**
     * 修改行程管理
     *
     * @param locationTrip 行程管理
     * @return 结果
     */
    public int updateLocationTrip(LocationTrip locationTrip);

    /**
     * 批量删除行程管理
     *
     * @param ids 需要删除的行程管理主键集合
     * @return 结果
     */
    public int deleteLocationTripByIds(Long[] ids);

    /**
     * 删除行程管理信息
     *
     * @param id 行程管理主键
     * @return 结果
     */
    public int deleteLocationTripById(Long id);

    /**
     * 批量插入行程轨迹点
     *
     * @param details 轨迹点列表
     * @return 结果
     */
    public int batchInsertTripDetails(List<LocationTripDetail> details);

    /**
     * 获取用户最新的轨迹点信息
     *
     * @param userId 用户ID
     * @return 最新的轨迹点信息
     */
    public LocationTripDetail selectLatestLocationByUserId(String userId);

    /**
     * 获取当天行程统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getTodayTripStats(String userId);

    /**
     * 查询行程管理详细信息（包含车辆信息）
     *
     * @param id 行程管理主键
     * @return 行程管理详细信息
     */
    public LocationTripInfoDTO selectLocationTripInfoById(Long id);

    /**
     * 获取用户最新的轨迹点信息和车辆总里程
     *
     * @param userId 用户ID
     * @param carId 车辆ID
     * @return 最新轨迹点和总里程信息
     */
    Map<String, Object> selectLatestLocationAndMileage(String userId, Long carId);

    /**
     * 根据本地ID和用户ID查询行程
     *
     * @param localId 本地ID
     * @param userId 用户ID
     * @return 行程信息
     */
    public LocationTrip selectLocationTripByLocalId(Long localId, String userId);
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.location.mapper.LocationTripMapper">

    <resultMap type="LocationTrip" id="LocationTripResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="carId"    column="car_id"    />
        <result property="userName"    column="user_name"    />
        <result property="localId"    column="local_id"    />
        <result property="type"    column="type"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="totalDistance"    column="total_distance"    />
        <result property="totalTime"    column="total_time"    />
        <result property="activeTime"    column="active_time"    />
        <result property="maxAltitude"    column="max_altitude"    />
        <result property="minAltitude"    column="min_altitude"    />
        <result property="avgAltitude"    column="avg_altitude"    />
        <result property="maxSpeed"    column="max_speed"    />
        <result property="avgSpeed"    column="avg_speed"    />
        <result property="maxAcceleration"    column="max_acceleration"    />
        <result property="startLatitude"    column="start_latitude"    />
        <result property="startLongitude"    column="start_longitude"    />
        <result property="endLatitude"    column="end_latitude"    />
        <result property="endLongitude"    column="end_longitude"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="LocationTripLocationTripDetailResult" type="LocationTrip" extends="LocationTripResult">
        <collection property="locationTripDetailList" ofType="LocationTripDetail" column="id" select="selectLocationTripDetailList" />
    </resultMap>

    <resultMap type="LocationTripDetail" id="LocationTripDetailResult">
        <result property="id"    column="id"    />
        <result property="tripId"    column="trip_id"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="timestamp"    column="timestamp"    />
        <result property="accuracy"    column="accuracy"    />
        <result property="altitude"    column="altitude"    />
        <result property="speed"    column="speed"    />
        <result property="bearing"    column="bearing"    />
        <result property="satellites"    column="satellites"    />
        <result property="provider"    column="provider"    />
        <result property="locationProvider"    column="location_provider"    />
        <result property="isUploaded"    column="is_uploaded"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectLocationTripVo">
        select t.id,
               t.user_id,
               t.car_id,
               u.nick_name as user_name,
               t.local_id,
               t.type,
               t.start_time,
               t.end_time,
               t.total_distance,
               t.total_time,
               t.active_time,
               t.max_altitude,
               t.min_altitude,
               t.avg_altitude,
               t.max_speed,
               t.avg_speed,
               t.max_acceleration,
               t.start_latitude,
               t.start_longitude,
               t.end_latitude,
               t.end_longitude,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time
        from location_trip t
                 left join sys_user u on t.user_id = u.user_id
    </sql>

    <select id="selectLocationTripList" parameterType="LocationTrip" resultMap="LocationTripResult">
        <include refid="selectLocationTripVo"/>
        <where>
            <if test="userId != null and userId != ''"> and t.user_id = #{userId}</if>
            <if test="carId != null"> and t.car_id = #{carId}</if>
            <if test="startTime != null"> and t.start_time >= #{startTime}</if>
            <if test="endTime != null"> and t.end_time &lt;= #{endTime}</if>
            <if test="userName != null and userName != ''">
                AND u.nick_name like concat('%', #{userName}, '%')
            </if>
            ${params.dataScope}
        </where>
    </select>

    <select id="selectLocationTripById" parameterType="Long" resultMap="LocationTripLocationTripDetailResult">
        select id, user_id, car_id, local_id, type, start_time, end_time, total_distance, total_time, active_time, max_altitude, min_altitude, avg_altitude, max_speed, avg_speed, max_acceleration, start_latitude, start_longitude, end_latitude, end_longitude, create_time, update_time
        from location_trip
        where id = #{id}
    </select>

    <select id="selectLocationTripDetailList" resultMap="LocationTripDetailResult">
        select id, trip_id, latitude, longitude, timestamp, accuracy, altitude, speed, bearing, satellites, provider, location_provider, is_uploaded, create_time
        from location_trip_detail
        where trip_id = #{trip_id}
    </select>

    <insert id="insertLocationTrip" parameterType="LocationTrip" useGeneratedKeys="true" keyProperty="id">
        insert into location_trip
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="carId != null">car_id,</if>
            <if test="localId != null">local_id,</if>
            <if test="type != null">type,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="totalDistance != null">total_distance,</if>
            <if test="totalTime != null">total_time,</if>
            <if test="activeTime != null">active_time,</if>
            <if test="maxAltitude != null">max_altitude,</if>
            <if test="minAltitude != null">min_altitude,</if>
            <if test="avgAltitude != null">avg_altitude,</if>
            <if test="maxSpeed != null">max_speed,</if>
            <if test="avgSpeed != null">avg_speed,</if>
            <if test="maxAcceleration != null">max_acceleration,</if>
            <if test="startLatitude != null">start_latitude,</if>
            <if test="startLongitude != null">start_longitude,</if>
            <if test="endLatitude != null">end_latitude,</if>
            <if test="endLongitude != null">end_longitude,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="carId != null">#{carId},</if>
            <if test="localId != null">#{localId},</if>
            <if test="type != null">#{type},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="totalDistance != null">#{totalDistance},</if>
            <if test="totalTime != null">#{totalTime},</if>
            <if test="activeTime != null">#{activeTime},</if>
            <if test="maxAltitude != null">#{maxAltitude},</if>
            <if test="minAltitude != null">#{minAltitude},</if>
            <if test="avgAltitude != null">#{avgAltitude},</if>
            <if test="maxSpeed != null">#{maxSpeed},</if>
            <if test="avgSpeed != null">#{avgSpeed},</if>
            <if test="maxAcceleration != null">#{maxAcceleration},</if>
            <if test="startLatitude != null">#{startLatitude},</if>
            <if test="startLongitude != null">#{startLongitude},</if>
            <if test="endLatitude != null">#{endLatitude},</if>
            <if test="endLongitude != null">#{endLongitude},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateLocationTrip" parameterType="LocationTrip">
        update location_trip
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="carId != null">car_id = #{carId},</if>
            <if test="localId != null">local_id = #{localId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="totalDistance != null">total_distance = #{totalDistance},</if>
            <if test="totalTime != null">total_time = #{totalTime},</if>
            <if test="activeTime != null">active_time = #{activeTime},</if>
            <if test="maxAltitude != null">max_altitude = #{maxAltitude},</if>
            <if test="minAltitude != null">min_altitude = #{minAltitude},</if>
            <if test="avgAltitude != null">avg_altitude = #{avgAltitude},</if>
            <if test="maxSpeed != null">max_speed = #{maxSpeed},</if>
            <if test="avgSpeed != null">avg_speed = #{avgSpeed},</if>
            <if test="maxAcceleration != null">max_acceleration = #{maxAcceleration},</if>
            <if test="startLatitude != null">start_latitude = #{startLatitude},</if>
            <if test="startLongitude != null">start_longitude = #{startLongitude},</if>
            <if test="endLatitude != null">end_latitude = #{endLatitude},</if>
            <if test="endLongitude != null">end_longitude = #{endLongitude},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLocationTripById" parameterType="Long">
        delete from location_trip where id = #{id}
    </delete>

    <delete id="deleteLocationTripByIds" parameterType="String">
        delete from location_trip where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteLocationTripDetailByTripIds" parameterType="String">
        delete from location_trip_detail where trip_id in
        <foreach item="tripId" collection="array" open="(" separator="," close=")">
            #{tripId}
        </foreach>
    </delete>

    <delete id="deleteLocationTripDetailByTripId" parameterType="Long">
        delete from location_trip_detail where trip_id = #{tripId}
    </delete>

    <insert id="batchLocationTripDetail">
        insert into location_trip_detail(
        trip_id, latitude, longitude, timestamp,
        accuracy, altitude, speed, bearing,
        satellites, provider, location_provider,
        is_uploaded, create_time
        ) values
        <foreach item="item" index="index" collection="list" separator=",">
            (
            #{item.tripId}, #{item.latitude}, #{item.longitude}, #{item.timestamp},
            #{item.accuracy}, #{item.altitude}, #{item.speed}, #{item.bearing},
            #{item.satellites}, #{item.provider}, #{item.locationProvider},
            #{item.isUploaded}, sysdate()
            )
        </foreach>
    </insert>

    <!-- 获取用户最新的轨迹点信息 -->
    <select id="selectLatestLocationByUserId" resultMap="LocationTripDetailResult">
        SELECT d.*
        FROM location_trip_detail d
                 INNER JOIN location_trip t ON d.trip_id = t.id
        WHERE t.user_id = #{userId}
        ORDER BY d.timestamp DESC
            LIMIT 1
    </select>

    <!-- 查询当天行程统计信息 -->
    <select id="selectTodayTripStats" resultType="java.util.Map">
        SELECT
            COUNT(*) as tripCount,
            COALESCE(SUM(total_distance), 0) as totalDistance,
            COALESCE(SUM(total_time), 0) as totalTime,
            COALESCE(AVG(NULLIF(avg_speed, 0)), 0) as avgSpeed,
            COALESCE(MAX(NULLIF(max_speed, 0)), 0) as maxSpeed
        FROM location_trip
        WHERE user_id = #{userId}
            AND DATE(FROM_UNIXTIME(start_time/1000)) = CURDATE()
    </select>

    <!-- 获取用户指定车辆的最新轨迹点信息 -->
    <select id="selectLatestLocationByUserIdAndCarId" resultMap="LocationTripDetailResult">
        SELECT d.*
        FROM location_trip_detail d
                 INNER JOIN location_trip t ON d.trip_id = t.id
        WHERE t.user_id = #{userId}
          <if test="carId != null">
              AND t.car_id = #{carId}
          </if>
        ORDER BY d.timestamp DESC
            LIMIT 1
    </select>

    <!-- 根据本地ID和用户ID查询行程 -->
    <select id="selectLocationTripByLocalIdAndUserId" resultMap="LocationTripResult">
        <include refid="selectLocationTripVo"/>
        where t.local_id = #{localId} and t.user_id = #{userId}
    </select>
</mapper>
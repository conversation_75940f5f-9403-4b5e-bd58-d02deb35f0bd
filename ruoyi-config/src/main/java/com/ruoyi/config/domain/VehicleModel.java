package com.ruoyi.config.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 车型对象 models
 * 
 * <AUTHOR>
 * @date 2024-08-21
 */
public class VehicleModel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 车型ID */
    private String id;

    /** 品牌ID */
    @Excel(name = "品牌ID")
    private String brandId;

    /** 车型名称 */
    @Excel(name = "车型名称")
    private String name;
    
    /** 车型图片 */
    @Excel(name = "车型图片")
    private String image;
    
    /** 是否新车型 */
    @Excel(name = "是否新车型")
    private Boolean isNew;
    
    /** 是否热门 */
    @Excel(name = "是否热门")
    private Boolean isHot;
    
    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    // getter和setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public Boolean getIsNew() {
        return isNew;
    }

    public void setIsNew(Boolean isNew) {
        this.isNew = isNew;
    }

    public Boolean getIsHot() {
        return isHot;
    }

    public void setIsHot(Boolean isHot) {
        this.isHot = isHot;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("brandId", getBrandId())
            .append("name", getName())
            .append("image", getImage())
            .append("isNew", getIsNew())
            .append("isHot", getIsHot())
            .append("status", getStatus())
            .toString();
    }
}

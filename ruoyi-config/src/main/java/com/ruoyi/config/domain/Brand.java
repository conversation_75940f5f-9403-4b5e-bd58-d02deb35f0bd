package com.ruoyi.config.domain;

import java.util.List;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 品牌目录对象 brands
 * 
 * <AUTHOR>
 * @date 2024-08-21
 */
public class Brand extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 品牌ID */
    private String id;

    /** 品牌名称 */
    @Excel(name = "品牌名称")
    private String name;
    
    /** 品牌logo */
    @Excel(name = "品牌logo")
    private String logo;
    
    /** logo描述 */
    @Excel(name = "logo描述")
    private String logoText;
    
    /** 品牌首字母 */
    @Excel(name = "品牌首字母")
    private String letter;
    
    /** 是否热门 */
    @Excel(name = "是否热门")
    private Boolean isHot;
    
    /** 热门排序 */
    @Excel(name = "热门排序")
    private Integer hotIndex;
    
    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    /** 车型列表 */
    private List<VehicleModel> modelList;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogoText() {
        return logoText;
    }

    public void setLogoText(String logoText) {
        this.logoText = logoText;
    }

    public String getLetter() {
        return letter;
    }

    public void setLetter(String letter) {
        this.letter = letter;
    }

    public Boolean getIsHot() {
        return isHot;
    }

    public void setIsHot(Boolean isHot) {
        this.isHot = isHot;
    }

    public Integer getHotIndex() {
        return hotIndex;
    }

    public void setHotIndex(Integer hotIndex) {
        this.hotIndex = hotIndex;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<VehicleModel> getModelList() {
        return modelList;
    }

    public void setModelList(List<VehicleModel> modelList) {
        this.modelList = modelList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("logo", getLogo())
            .append("logoText", getLogoText())
            .append("letter", getLetter())
            .append("isHot", getIsHot())
            .append("hotIndex", getHotIndex())
            .append("status", getStatus())
            .append("modelList", getModelList())
            .toString();
    }
}

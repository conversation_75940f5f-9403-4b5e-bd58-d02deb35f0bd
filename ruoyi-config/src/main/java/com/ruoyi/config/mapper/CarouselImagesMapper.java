package com.ruoyi.config.mapper;

import java.util.List;
import com.ruoyi.config.domain.CarouselImages;

/**
 * 轮播图管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-06
 */
public interface CarouselImagesMapper 
{
    /**
     * 查询轮播图管理
     * 
     * @param id 轮播图管理主键
     * @return 轮播图管理
     */
    public CarouselImages selectCarouselImagesById(Long id);

    /**
     * 查询轮播图管理列表
     * 
     * @param carouselImages 轮播图管理
     * @return 轮播图管理集合
     */
    public List<CarouselImages> selectCarouselImagesList(CarouselImages carouselImages);

    /**
     * 新增轮播图管理
     * 
     * @param carouselImages 轮播图管理
     * @return 结果
     */
    public int insertCarouselImages(CarouselImages carouselImages);

    /**
     * 修改轮播图管理
     * 
     * @param carouselImages 轮播图管理
     * @return 结果
     */
    public int updateCarouselImages(CarouselImages carouselImages);

    /**
     * 删除轮播图管理
     * 
     * @param id 轮播图管理主键
     * @return 结果
     */
    public int deleteCarouselImagesById(Long id);

    /**
     * 批量删除轮播图管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCarouselImagesByIds(Long[] ids);
}

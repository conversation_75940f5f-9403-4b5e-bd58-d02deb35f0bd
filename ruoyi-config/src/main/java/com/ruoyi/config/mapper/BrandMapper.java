package com.ruoyi.config.mapper;

import java.util.List;
import com.ruoyi.config.domain.Brand;
import com.ruoyi.config.domain.VehicleModel;

/**
 * 品牌目录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface BrandMapper 
{
    /**
     * 查询品牌目录
     * 
     * @param id 品牌目录主键
     * @return 品牌目录
     */
    public Brand selectBrandById(String id);

    /**
     * 查询品牌目录列表
     * 
     * @param brand 品牌目录
     * @return 品牌目录集合
     */
    public List<Brand> selectBrandList(Brand brand);

    /**
     * 新增品牌目录
     * 
     * @param brand 品牌目录
     * @return 结果
     */
    public int insertBrand(Brand brand);

    /**
     * 修改品牌目录
     * 
     * @param brand 品牌目录
     * @return 结果
     */
    public int updateBrand(Brand brand);

    /**
     * 删除品牌目录
     * 
     * @param id 品牌目录主键
     * @return 结果
     */
    public int deleteBrandById(String id);

    /**
     * 批量删除品牌目录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBrandByIds(String[] ids);

    /**
     * 批量删除${subTable.functionName}
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVehicleModelByModels(String[] ids);
    
    /**
     * 批量新增${subTable.functionName}
     * 
     * @param vehicleModelList ${subTable.functionName}列表
     * @return 结果
     */
    public int batchVehicleModel(List<VehicleModel> vehicleModelList);
    

    /**
     * 通过品牌目录主键删除${subTable.functionName}信息
     * 
     * @param id 品牌目录ID
     * @return 结果
     */
    public int deleteVehicleModelByModel(String id);
}

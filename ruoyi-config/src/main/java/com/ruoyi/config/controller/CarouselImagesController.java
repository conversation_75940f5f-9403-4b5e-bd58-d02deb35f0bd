package com.ruoyi.config.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.config.domain.CarouselImages;
import com.ruoyi.config.service.ICarouselImagesService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 轮播图管理Controller
 * 
 * <AUTHOR>
 * @date 2024-09-06
 */
@RestController
@RequestMapping("/config/banner")
public class CarouselImagesController extends BaseController
{
    @Autowired
    private ICarouselImagesService carouselImagesService;

    /**
     * 查询轮播图管理列表
     */
   // @PreAuthorize("@ss.hasPermi('config:banner:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarouselImages carouselImages)
    {
        startPage();
        List<CarouselImages> list = carouselImagesService.selectCarouselImagesList(carouselImages);
        return getDataTable(list);
    }

    /**
     * 导出轮播图管理列表
     */
    @PreAuthorize("@ss.hasPermi('config:banner:export')")
    @Log(title = "轮播图管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarouselImages carouselImages)
    {
        List<CarouselImages> list = carouselImagesService.selectCarouselImagesList(carouselImages);
        ExcelUtil<CarouselImages> util = new ExcelUtil<CarouselImages>(CarouselImages.class);
        util.exportExcel(response, list, "轮播图管理数据");
    }

    /**
     * 获取轮播图管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('config:banner:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(carouselImagesService.selectCarouselImagesById(id));
    }

    /**
     * 新增轮播图管理
     */
    @PreAuthorize("@ss.hasPermi('config:banner:add')")
    @Log(title = "轮播图管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarouselImages carouselImages)
    {
        carouselImages.setCreateBy(getUsername());
        return toAjax(carouselImagesService.insertCarouselImages(carouselImages));
    }

    /**
     * 修改轮播图管理
     */
    @PreAuthorize("@ss.hasPermi('config:banner:edit')")
    @Log(title = "轮播图管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CarouselImages carouselImages)
    {
        carouselImages.setUpdateBy(getUsername());
        return toAjax(carouselImagesService.updateCarouselImages(carouselImages));
    }

    /**
     * 删除轮播图管理
     */
    @PreAuthorize("@ss.hasPermi('config:banner:remove')")
    @Log(title = "轮播图管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(carouselImagesService.deleteCarouselImagesByIds(ids));
    }
}

package com.ruoyi.config.service;

import java.util.List;
import com.ruoyi.config.domain.Brand;

/**
 * 品牌目录Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IBrandService 
{
    /**
     * 查询品牌目录
     * 
     * @param id 品牌目录主键
     * @return 品牌目录
     */
    public Brand selectBrandById(String id);

    /**
     * 查询品牌目录列表
     * 
     * @param brand 品牌目录
     * @return 品牌目录集合
     */
    public List<Brand> selectBrandList(Brand brand);

    /**
     * 新增品牌目录
     * 
     * @param brand 品牌目录
     * @return 结果
     */
    public int insertBrand(Brand brand);

    /**
     * 修改品牌目录
     * 
     * @param brand 品牌目录
     * @return 结果
     */
    public int updateBrand(Brand brand);

    /**
     * 批量删除品牌目录
     * 
     * @param ids 需要删除的品牌目录主键集合
     * @return 结果
     */
    public int deleteBrandByIds(String[] ids);

    /**
     * 删除品牌目录信息
     * 
     * @param id 品牌目录主键
     * @return 结果
     */
    public int deleteBrandById(String id);
}

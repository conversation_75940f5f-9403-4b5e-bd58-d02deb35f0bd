package com.ruoyi.config.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.config.mapper.CarouselImagesMapper;
import com.ruoyi.config.domain.CarouselImages;
import com.ruoyi.config.service.ICarouselImagesService;

/**
 * 轮播图管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-06
 */
@Service
public class CarouselImagesServiceImpl implements ICarouselImagesService 
{
    @Autowired
    private CarouselImagesMapper carouselImagesMapper;

    /**
     * 查询轮播图管理
     * 
     * @param id 轮播图管理主键
     * @return 轮播图管理
     */
    @Override
    public CarouselImages selectCarouselImagesById(Long id)
    {
        return carouselImagesMapper.selectCarouselImagesById(id);
    }

    /**
     * 查询轮播图管理列表
     * 
     * @param carouselImages 轮播图管理
     * @return 轮播图管理
     */
    @Override
    public List<CarouselImages> selectCarouselImagesList(CarouselImages carouselImages)
    {
        return carouselImagesMapper.selectCarouselImagesList(carouselImages);
    }

    /**
     * 新增轮播图管理
     * 
     * @param carouselImages 轮播图管理
     * @return 结果
     */
    @Override
    public int insertCarouselImages(CarouselImages carouselImages)
    {
        return carouselImagesMapper.insertCarouselImages(carouselImages);
    }

    /**
     * 修改轮播图管理
     * 
     * @param carouselImages 轮播图管理
     * @return 结果
     */
    @Override
    public int updateCarouselImages(CarouselImages carouselImages)
    {
        return carouselImagesMapper.updateCarouselImages(carouselImages);
    }

    /**
     * 批量删除轮播图管理
     * 
     * @param ids 需要删除的轮播图管理主键
     * @return 结果
     */
    @Override
    public int deleteCarouselImagesByIds(Long[] ids)
    {
        return carouselImagesMapper.deleteCarouselImagesByIds(ids);
    }

    /**
     * 删除轮播图管理信息
     * 
     * @param id 轮播图管理主键
     * @return 结果
     */
    @Override
    public int deleteCarouselImagesById(Long id)
    {
        return carouselImagesMapper.deleteCarouselImagesById(id);
    }
}

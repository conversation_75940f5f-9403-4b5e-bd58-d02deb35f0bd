package com.ruoyi.config.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.config.domain.VehicleModel;
import com.ruoyi.config.mapper.BrandMapper;
import com.ruoyi.config.domain.Brand;
import com.ruoyi.config.service.IBrandService;

/**
 * 品牌目录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class BrandServiceImpl implements IBrandService 
{
    @Autowired
    private BrandMapper brandMapper;

    /**
     * 查询品牌目录
     * 
     * @param id 品牌目录主键
     * @return 品牌目录
     */
    @Override
    public Brand selectBrandById(String id)
    {
        return brandMapper.selectBrandById(id);
    }

    /**
     * 查询品牌目录列表
     * 
     * @param brand 品牌目录
     * @return 品牌目录
     */
    @Override
    public List<Brand> selectBrandList(Brand brand)
    {
        return brandMapper.selectBrandList(brand);
    }

    /**
     * 新增品牌目录
     * 
     * @param brand 品牌目录
     * @return 结果
     */
    @Transactional
    @Override
    public int insertBrand(Brand brand)
    {
        int rows = brandMapper.insertBrand(brand);
        insertVehicleModel(brand);
        return rows;
    }

    /**
     * 修改品牌目录
     * 
     * @param brand 品牌目录
     * @return 结果
     */
    @Transactional
    @Override
    public int updateBrand(Brand brand)
    {
        brandMapper.deleteVehicleModelByModel(brand.getId());
        insertVehicleModel(brand);
        return brandMapper.updateBrand(brand);
    }

    /**
     * 批量删除品牌目录
     * 
     * @param ids 需要删除的品牌目录主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteBrandByIds(String[] ids)
    {
        brandMapper.deleteVehicleModelByModels(ids);
        return brandMapper.deleteBrandByIds(ids);
    }

    /**
     * 删除品牌目录信息
     * 
     * @param id 品牌目录主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteBrandById(String id)
    {
        brandMapper.deleteVehicleModelByModel(id);
        return brandMapper.deleteBrandById(id);
    }

    /**
     * 新增${subTable.functionName}信息
     * 
     * @param brand 品牌目录对象
     */
    public void insertVehicleModel(Brand brand)
    {
        List<VehicleModel> vehicleModelList = brand.getModelList();
        String id = brand.getId();
        if (StringUtils.isNotNull(vehicleModelList))
        {
            List<VehicleModel> list = new ArrayList<VehicleModel>();
            for (VehicleModel vehicleModel : vehicleModelList)
            {
                vehicleModel.setBrandId(id);
                list.add(vehicleModel);
            }
            if (list.size() > 0)
            {
                brandMapper.batchVehicleModel(list);
            }
        }
    }
}

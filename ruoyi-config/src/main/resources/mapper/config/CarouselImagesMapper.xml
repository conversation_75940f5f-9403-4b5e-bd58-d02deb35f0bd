<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.config.mapper.CarouselImagesMapper">
    
    <resultMap type="CarouselImages" id="CarouselImagesResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="imgPath"    column="img_path"    />
        <result property="linkUrl"    column="link_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"   column="create_by"   />
        <result property="createTime" column="create_time" />
        <result property="updateBy"   column="update_by"   />
        <result property="updateTime" column="update_time" />
        <result property="remark"    column="remark"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectCarouselImagesVo">
        select id, title, img_path, link_url, sort_order, start_time, end_time, status, create_by, create_time, remark, type from carousel_images
    </sql>

    <select id="selectCarouselImagesList" parameterType="CarouselImages" resultMap="CarouselImagesResult">
        <include refid="selectCarouselImagesVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
        </where>
    </select>
    
    <select id="selectCarouselImagesById" parameterType="Long" resultMap="CarouselImagesResult">
        <include refid="selectCarouselImagesVo"/>
        where id = #{id}
    </select>

    <insert id="insertCarouselImages" parameterType="CarouselImages" useGeneratedKeys="true" keyProperty="id">
        insert into carousel_images
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="imgPath != null and imgPath != ''">img_path,</if>
            <if test="linkUrl != null">link_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null">remark,</if>
            <if test="type != null">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="imgPath != null and imgPath != ''">#{imgPath},</if>
            <if test="linkUrl != null">#{linkUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="type != null">#{type},</if>
         </trim>
    </insert>

    <update id="updateCarouselImages" parameterType="CarouselImages">
        update carousel_images
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="imgPath != null and imgPath != ''">img_path = #{imgPath},</if>
            <if test="linkUrl != null">link_url = #{linkUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
            <if test="remark != null">remark = #{remark},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCarouselImagesById" parameterType="Long">
        delete from carousel_images where id = #{id}
    </delete>

    <delete id="deleteCarouselImagesByIds" parameterType="String">
        delete from carousel_images where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
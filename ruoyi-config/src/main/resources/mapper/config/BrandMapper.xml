<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.config.mapper.BrandMapper">
    
    <resultMap type="Brand" id="BrandResult">
        <result property="id" column="id" />
        <result property="name" column="name" />
        <result property="logo" column="logo" />
        <result property="logoText" column="logo_text" />
        <result property="letter" column="letter" />
        <result property="isHot" column="is_hot" />
        <result property="hotIndex" column="hot_index" />
        <result property="status" column="status" />
    </resultMap>

    <resultMap id="BrandModelResult" type="Brand" extends="BrandResult">
        <collection property="modelList" ofType="VehicleModel">
            <result property="id" column="model_id" />
            <result property="brandId" column="brand_id" />
            <result property="name" column="model_name" />
            <result property="image" column="image" />
            <result property="isNew" column="is_new" />
            <result property="isHot" column="model_is_hot" />
            <result property="status" column="model_status" />
        </collection>
    </resultMap>

    <sql id="selectBrandVo">
        select id, name, logo, logo_text, letter, is_hot, hot_index, status 
        from brands
    </sql>

    <select id="selectBrandList" parameterType="Brand" resultMap="BrandResult">
        <include refid="selectBrandVo"/>
        <where>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="letter != null and letter != ''">
                AND letter = #{letter}
            </if>
            <if test="isHot != null">
                AND is_hot = #{isHot}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        order by hot_index desc
    </select>
    
    <select id="selectBrandById" parameterType="String" resultMap="BrandModelResult">
        select b.id, b.name, b.logo, b.logo_text, b.letter, b.is_hot, b.hot_index, b.status,
               m.id as model_id, m.brand_id, m.name as model_name, m.image, 
               m.is_new, m.is_hot as model_is_hot, m.status as model_status
        from brands b
        left join models m on b.id = m.brand_id
        where b.id = #{id}
    </select>

    <insert id="insertBrand" parameterType="Brand">
        insert into brands
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="logo != null">logo,</if>
            <if test="logoText != null">logo_text,</if>
            <if test="letter != null">letter,</if>
            <if test="isHot != null">is_hot,</if>
            <if test="hotIndex != null">hot_index,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="logo != null">#{logo},</if>
            <if test="logoText != null">#{logoText},</if>
            <if test="letter != null">#{letter},</if>
            <if test="isHot != null">#{isHot},</if>
            <if test="hotIndex != null">#{hotIndex},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateBrand" parameterType="Brand">
        update brands
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="logo != null">logo = #{logo},</if>
            <if test="logoText != null">logo_text = #{logoText},</if>
            <if test="letter != null">letter = #{letter},</if>
            <if test="isHot != null">is_hot = #{isHot},</if>
            <if test="hotIndex != null">hot_index = #{hotIndex},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBrandById" parameterType="String">
        delete from brands where id = #{id}
    </delete>

    <delete id="deleteBrandByIds" parameterType="String">
        delete from brands where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
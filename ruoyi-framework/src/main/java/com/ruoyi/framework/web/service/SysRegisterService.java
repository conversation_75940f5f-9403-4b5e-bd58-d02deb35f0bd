package com.ruoyi.framework.web.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.UserAuthInfo;
import com.ruoyi.common.core.domain.model.MiniprogrammRegisterBody;
import com.ruoyi.common.core.domain.model.RegisterBody;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.system.mapper.UserAuthInfoMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.WechatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 注册校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysRegisterService {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private UserAuthInfoMapper userAuthInfoMapper;
    @Autowired
    private WechatService wechatService;

    /**
     * 注册
     */
    public String register(RegisterBody registerBody) {
        String msg = "", username = registerBody.getUsername(), password = registerBody.getPassword();
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setEmail(registerBody.getEmail());
        sysUser.setPhonenumber(registerBody.getPhoneNumber());
        sysUser.setAvatar(registerBody.getAvatar());
        // 验证码开关
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled) {
            validateCaptcha(username, registerBody.getCode(), registerBody.getUuid());
        }
        // 查询openid是否存在
        String wxCode = registerBody.getWxCode();
        String openId = null;
        if (wxCode != null) {
            openId = wechatService.getOpenId(registerBody.getWxCode());
            UserAuthInfo byOpenId = userAuthInfoMapper.findByOpenId(openId);
            if (byOpenId != null) {
                msg = "该openid已存在";
                return msg;
            }
        }

        if (StringUtils.isEmpty(username)) {
            msg = "用户名不能为空";
        } else if (StringUtils.isEmpty(password)) {
            msg = "用户密码不能为空";
        } else if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            msg = "账户长度必须在2到20个字符之间";
        } else if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            msg = "密码长度必须在5到20个字符之间";
        } else if (!userService.checkUserNameUnique(sysUser)) {
            msg = "保存用户'" + username + "'失败，注册账号已存在";
        } else {
            sysUser.setNickName(username);
            sysUser.setPwdUpdateDate(DateUtils.getNowDate());
            sysUser.setPassword(SecurityUtils.encryptPassword(password));
            try {
                userService.registerUser(sysUser);
                //关联openid和用户id
                if (openId != null) {
                    UserAuthInfo userAuthInfo = new UserAuthInfo();
                    userAuthInfo.setOpenId(openId);
                    userAuthInfo.setUserId(sysUser.getUserId());
                    userAuthInfo.setUserName(username);
                    userAuthInfo.setAppType(registerBody.getAppType());
                    userAuthInfoMapper.insert(userAuthInfo);
                }

                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.REGISTER, MessageUtils.message("user.register.success")));

            } catch (Exception e) {
                msg = "注册失败，请联系系统管理人员";
            }

        }
        return msg;
    }

    /**
     * 小程序注册
     */
    public String registerWx(MiniprogrammRegisterBody registerBody) {
        String msg = "", username = registerBody.getUserName(), password = registerBody.getPassword();
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setEmail(registerBody.getEmail());
        sysUser.setPhonenumber(registerBody.getPhonenumber());
        sysUser.setAvatar(registerBody.getAvatar());
        sysUser.setDeptId(registerBody.getDeptId());
        sysUser.setPostIds(registerBody.getPostIds());
        sysUser.setRoleIds(registerBody.getRoleIds());
        sysUser.setNickName(registerBody.getNickName());
        // 验证码开关
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled) {
            validateCaptcha(username, registerBody.getCode(), registerBody.getUuid());
        }
        // 查询openid是否存在
        String wxCode = registerBody.getWxCode();
        String openId = null;
        if (wxCode != null) {
            openId = wechatService.getOpenId(registerBody.getWxCode());
            UserAuthInfo byOpenId = userAuthInfoMapper.findByOpenId(openId);
            if (byOpenId != null) {
                msg = "该openid已存在";
                return msg;
            }
        }

        if (StringUtils.isEmpty(username)) {
            msg = "用户名不能为空";
        } else if (StringUtils.isEmpty(password)) {
            msg = "用户密码不能为空";
        } else if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            msg = "账户长度必须在2到20个字符之间";
        } else if (password.length() < UserConstants
                .PASSWORD_MIN_LENGTH || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            msg = "密码长度必须在5到20个字符之间";
        }
        else if (!userService.checkUserNameUnique(sysUser)) {
            msg = "保存用户'" + username + "'失败，注册账号已存在";
        }
        else {
            sysUser.setPassword(SecurityUtils.encryptPassword(password));
            boolean regFlag = userService.registerUser(sysUser);
            if (!regFlag) {
                msg = "注册失败,请联系系统管理人员";
            } else {
                //关联openid和用户id
                if (openId != null) {
                    UserAuthInfo userAuthInfo = new UserAuthInfo();
                    userAuthInfo.setOpenId(openId);
                    userAuthInfo.setUserId(sysUser.getUserId());
                    userAuthInfo.setUserName(username);
                    userAuthInfo.setAppType(registerBody.getAppType());
                    userAuthInfoMapper.insert(userAuthInfo);
                }

                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.REGISTER, MessageUtils.message("user.register.success")));
            }
        }
        return msg;
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid) {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null) {
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha)) {
            throw new CaptchaException();
        }
    }
}
